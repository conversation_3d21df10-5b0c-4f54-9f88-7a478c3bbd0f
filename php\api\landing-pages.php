<?php
require_once '../config.php';

header('Content-Type: application/json');

function generateUniqueUrl($productId) {
    return '/landing/product-' . $productId . '-' . uniqid();
}

function handleGet() {
    global $conn;
    
    try {
        $stmt = $conn->prepare(
            "SELECT lp.*, p.titre as product_title 
             FROM landing_pages lp 
             JOIN produits p ON lp.produit_id = p.id"
        );
        $stmt->execute();
        $pages = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        // Get images for each landing page
        foreach ($pages as &$page) {
            $stmt = $conn->prepare(
                "SELECT image_url 
                 FROM landing_page_images 
                 WHERE landing_page_id = ? 
                 ORDER BY ordre"
            );
            $stmt->execute([$page['id']]);
            $page['images'] = $stmt->fetchAll(PDO::FETCH_COLUMN);
        }
        
        echo json_encode(['success' => true, 'data' => $pages]);
    } catch (PDOException $e) {
        http_response_code(500);
        echo json_encode(['success' => false, 'message' => 'Database error']);
    }
}

function handlePost() {
    global $conn;
    
    try {
        $productId = $_POST['productSelect'];
        $title = $_POST['landingPageTitle'];
        $rightContent = $_POST['rightContent'];
        $leftContent = $_POST['leftContent'];
        $linkUrl = generateUniqueUrl($productId);
        
        $conn->beginTransaction();
        
        // Insert landing page
        $stmt = $conn->prepare(
            "INSERT INTO landing_pages 
             (produit_id, titre, contenu_droit, contenu_gauche, lien_url) 
             VALUES (?, ?, ?, ?, ?)"
        );
        $stmt->execute([$productId, $title, $rightContent, $leftContent, $linkUrl]);
        $landingPageId = $conn->lastInsertId();
        
        // Handle image uploads
        if (!empty($_FILES['landingPageImages'])) {
            $uploadDir = '../../uploads/products/landing/';
            if (!file_exists($uploadDir)) {
                mkdir($uploadDir, 0777, true);
            }
            
            foreach ($_FILES['landingPageImages']['tmp_name'] as $key => $tmpName) {
                $fileName = uniqid() . '_' . $_FILES['landingPageImages']['name'][$key];
                $filePath = $uploadDir . $fileName;
                
                if (move_uploaded_file($tmpName, $filePath)) {
                    $stmt = $conn->prepare(
                        "INSERT INTO landing_page_images 
                         (landing_page_id, image_url, ordre) 
                         VALUES (?, ?, ?)"
                    );
                    $stmt->execute([$landingPageId, '/uploads/products/landing/' . $fileName, $key]);
                }
            }
        }
        
        $conn->commit();
        echo json_encode(['success' => true, 'message' => 'Landing page created successfully']);
        
    } catch (PDOException $e) {
        $conn->rollBack();
        http_response_code(500);
        echo json_encode(['success' => false, 'message' => 'Database error']);
    } catch (Exception $e) {
        $conn->rollBack();
        http_response_code(500);
        echo json_encode(['success' => false, 'message' => 'Server error']);
    }
}

function handleDelete() {
    global $conn;
    
    try {
        $id = $_GET['id'];
        
        // Get image paths before deleting
        $stmt = $conn->prepare("SELECT image_url FROM landing_page_images WHERE landing_page_id = ?");
        $stmt->execute([$id]);
        $images = $stmt->fetchAll(PDO::FETCH_COLUMN);
        
        // Delete landing page (cascade will handle images in DB)
        $stmt = $conn->prepare("DELETE FROM landing_pages WHERE id = ?");
        $stmt->execute([$id]);
        
        // Delete physical image files
        foreach ($images as $image) {
            $filePath = '../../' . ltrim($image, '/');
            if (file_exists($filePath)) {
                unlink($filePath);
            }
        }
        
        echo json_encode(['success' => true, 'message' => 'Landing page deleted successfully']);
        
    } catch (PDOException $e) {
        http_response_code(500);
        echo json_encode(['success' => false, 'message' => 'Database error']);
    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode(['success' => false, 'message' => 'Server error']);
    }
}

// Route requests
switch ($_SERVER['REQUEST_METHOD']) {
    case 'GET':
        handleGet();
        break;
    case 'POST':
        handlePost();
        break;
    case 'DELETE':
        handleDelete();
        break;
    default:
        http_response_code(405);
        echo json_encode(['success' => false, 'message' => 'Method not allowed']);
}