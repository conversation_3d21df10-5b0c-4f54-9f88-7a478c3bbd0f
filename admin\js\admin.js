// Gestionnaire de notifications
const notificationManager = {
    container: null,
    checkInterval: null,
    unreadCount: 0,

    init() {
        this.container = document.getElementById('notificationsContainer');
        this.startPolling();
    },

    startPolling() {
        this.checkNotifications();
        this.checkInterval = setInterval(() => this.checkNotifications(), 30000); // Vérifier toutes les 30 secondes
    },

    async checkNotifications() {
        try {
            const response = await fetch('../php/notifications.php?action=unread');
            const notifications = await response.json();
            
            if (Array.isArray(notifications)) {
                // Mettre à jour le compteur
                this.unreadCount = notifications.length;
                this.updateBadge();
                
                // Afficher les nouvelles notifications
                notifications.forEach(notification => this.showNotification(notification));
            }
        } catch (error) {
            console.error('Erreur lors de la vérification des notifications:', error);
        }
    },

    showNotification(notification) {
        const existingNotif = document.querySelector(`[data-notification-id="${notification.id}"]`);
        if (existingNotif) return;

        const notifElement = document.createElement('div');
        notifElement.className = `notification ${notification.type} unread`;
        notifElement.dataset.notificationId = notification.id;

        notifElement.innerHTML = `
            <div class="notification-content">
                <div class="notification-title">${this.getNotificationTitle(notification.type)}</div>
                <div class="notification-message">${notification.message}</div>
            </div>
            <button class="notification-close" onclick="notificationManager.closeNotification(${notification.id})">
                &times;
            </button>
        `;

        this.container.appendChild(notifElement);

        // Jouer un son de notification
        const audio = new Audio('../assets/notification.mp3');
        audio.play();
    },

    getNotificationTitle(type) {
        const titles = {
            'new_order': 'طلب جديد',
            'payment_received': 'تم استلام الدفع',
            'low_stock': 'تنبيه المخزون'
        };
        return titles[type] || 'إشعار';
    },

    updateBadge() {
        const ordersLink = document.querySelector('[data-section="orders"]');
        let badge = ordersLink.querySelector('.notification-badge');
        
        if (this.unreadCount > 0) {
            if (!badge) {
                badge = document.createElement('span');
                badge.className = 'notification-badge';
                ordersLink.appendChild(badge);
            }
            badge.textContent = this.unreadCount;
        } else if (badge) {
            badge.remove();
        }
    },

    async closeNotification(id) {
        try {
            await fetch(`../php/notifications.php?action=mark_read&id=${id}`);
            const notif = document.querySelector(`[data-notification-id="${id}"]`);
            if (notif) {
                notif.remove();
                this.unreadCount = Math.max(0, this.unreadCount - 1);
                this.updateBadge();
            }
        } catch (error) {
            console.error('Erreur lors de la fermeture de la notification:', error);
        }
    }
};

// Vérifier l'authentification
function checkAuth() {
    fetch('../php/admin.php?action=check')
        .then(response => response.json())
        .then(data => {
            if (!data.logged_in) {
                window.location.href = 'login.html';
            }
        });
}

// Add View More link to products table
function addViewMoreLink(row, product) {
    if (product.has_landing_page && product.landing_page_enabled) {
        const actionsCell = row.querySelector('td:last-child');
        const viewMoreLink = document.createElement('a');
        viewMoreLink.href = `/product-landing.php?slug=${product.slug}`;
        viewMoreLink.className = 'action-button view-more';
        viewMoreLink.target = '_blank';
        viewMoreLink.innerHTML = '<i class="fas fa-external-link-alt"></i> عرض الصفحة';
        actionsCell.appendChild(viewMoreLink);

        const copyUrlBtn = document.createElement('button');
        copyUrlBtn.className = 'action-button copy-url';
        copyUrlBtn.innerHTML = '<i class="fas fa-copy"></i> نسخ الرابط';
        copyUrlBtn.onclick = (e) => {
            e.preventDefault();
            const url = `${window.location.origin}/product-landing.php?slug=${product.slug}`;
            navigator.clipboard.writeText(url).then(() => {
                alert('تم نسخ الرابط بنجاح');
            });
        };
        actionsCell.appendChild(copyUrlBtn);
    }
}

// Navigation dans le panneau d'administration
document.querySelectorAll('.admin-nav li').forEach(item => {
    item.addEventListener('click', function() {
        if (this.id === 'logoutBtn') {
            logout();
            return;
        }

        // Add filters if they don't exist
        if (this.getAttribute('data-section') === 'orders') {
            const ordersSection = document.getElementById('orders');
            if (!ordersSection.querySelector('.orders-filters')) {
                const filtersHtml = `
                    <div class="orders-filters">
                        <div class="filter-group">
                            <label for="statusFilter">الحالة:</label>
                            <select id="statusFilter">
                                <option value="">الكل</option>
                                <option value="en_attente">قيد الانتظار</option>
                                <option value="payé">تم الدفع</option>
                                <option value="expédié">تم الشحن</option>
                            </select>
                        </div>
                        <div class="filter-group">
                            <label for="dateFilter">التاريخ:</label>
                            <select id="dateFilter">
                                <option value="">كل التواريخ</option>
                                <option value="today">اليوم</option>
                                <option value="week">هذا الأسبوع</option>
                                <option value="month">هذا الشهر</option>
                            </select>
                        </div>
                    </div>
                `;
                ordersSection.insertAdjacentHTML('afterbegin', filtersHtml);
                
                // Add event listeners for filters
                const filters = ordersSection.querySelectorAll('select');
                filters.forEach(filter => {
                    filter.addEventListener('change', () => {
                        const activeFilters = {
                            status: document.getElementById('statusFilter').value,
                            date: document.getElementById('dateFilter').value
                        };
                        loadOrders(activeFilters);
                    });
                });
            }
        }

        // Mettre à jour la navigation active
        document.querySelectorAll('.admin-nav li').forEach(li => li.classList.remove('active'));
        this.classList.add('active');

        // Afficher la section correspondante
        const section = this.getAttribute('data-section');
        document.querySelectorAll('.content-section').forEach(sec => sec.classList.remove('active'));
        document.getElementById(section).classList.add('active');

        // Charger les données de la section
        if (section === 'books') loadBooks();
        if (section === 'orders') loadOrders();
        if (section === 'dashboard') loadDashboard();
    });
});

// Charger le tableau de bord
function loadDashboard() {
    // Charger les statistiques
    fetch('../php/books.php')
        .then(response => response.json())
        .then(data => {
            const totalBooksCard = document.getElementById('totalBooks');
            totalBooksCard.textContent = data.length;
            totalBooksCard.closest('.stat-card').onclick = () => {
                document.querySelector('[data-section="books"]').click();
            };
        });

    fetch('../php/orders.php')
        .then(response => response.json())
        .then(data => {
            const newOrders = data.filter(order => order.statut === 'en_attente').length;
            const newOrdersCard = document.getElementById('newOrders');
            newOrdersCard.textContent = newOrders;
            newOrdersCard.closest('.stat-card').onclick = () => {
                document.querySelector('[data-section="orders"]').click();
            };

            const totalSales = data.reduce((total, order) => {
                return total + (order.statut === 'payé' ? parseFloat(order.montant_total) : 0);
            }, 0);
            const totalSalesCard = document.getElementById('totalSales');
            totalSalesCard.textContent = `${totalSales.toFixed(2)} دج`;
            totalSalesCard.closest('.stat-card').onclick = () => {
                document.querySelector('[data-section="orders"]').click();
            };

            // Afficher les commandes récentes
            const recentOrders = data.slice(0, 5);
            const tbody = document.querySelector('#recentOrdersTable tbody');
            tbody.innerHTML = '';

            recentOrders.forEach(order => {
                const tr = document.createElement('tr');
                tr.style.cursor = 'pointer';
                tr.innerHTML = `
                    <td>#${order.id}</td>
                    <td>${order.nom_client}</td>
                    <td>${order.montant_total} دج</td>
                    <td><span class="status-badge status-${order.statut}">${getStatusText(order.statut)}</span></td>
                    <td>${new Date(order.date_commande).toLocaleDateString('ar-DZ')}</td>
                `;
                tr.onclick = () => {
                    document.querySelector('[data-section="orders"]').click();
                    setTimeout(() => showOrderDetails(order.id), 100);
                };
                tbody.appendChild(tr);
            });
        });
}

// Charger la liste des livres
function loadBooks() {
    fetch('../php/books.php')
        .then(response => response.json())
        .then(data => {
            const tbody = document.querySelector('#booksTable tbody');
            tbody.innerHTML = '';

            data.forEach(book => {
                const tr = document.createElement('tr');
                tr.innerHTML = `
                    <td><img src="../${book.image_url}" alt="${book.titre}" width="50"></td>
                    <td>${book.titre}</td>
                    <td>${book.auteur}</td>
                    <td>${book.prix} دج</td>
                    <td>${book.stock}</td>
                    <td>
                        <button onclick="editBook(${book.id})" class="action-button">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button onclick="deleteBook(${book.id})" class="action-button" style="background: #e74c3c;">
                            <i class="fas fa-trash"></i>
                        </button>
                    </td>
                `;
                tbody.appendChild(tr);
            });
        });
}

// Charger la liste des commandes
async function loadOrders(filters = {}) {
    try {
        let url = '../php/orders.php';
        if (Object.keys(filters).length > 0) {
            const params = new URLSearchParams(filters);
            url += '?' + params.toString();
        }
        
        const response = await fetch(url);
        const orders = await response.json();
        const ordersTableBody = document.querySelector('#ordersTable tbody');
        ordersTableBody.innerHTML = '';

        orders.forEach(order => {
            const tr = document.createElement('tr');
            tr.innerHTML = `
                <td>#${order.id}</td>
                <td>
                    <div class="customer-info">
                        <strong>${order.nom_client}</strong>
                        <span class="customer-email">${order.email || ''}</span>
                    </div>
                </td>
                <td>
                    <div class="order-details">
                        <strong>${order.montant_total} دج</strong>
                        <span class="items-count">${order.items ? order.items.length : 0} منتجات</span>
                    </div>
                </td>
                <td><span class="status-badge status-${order.statut}">${getStatusText(order.statut)}</span></td>
                <td>${new Date(order.date_commande).toLocaleDateString('ar-DZ')}</td>
                <td>
                    <button onclick="showOrderDetails(${order.id})" class="action-button"><i class="fas fa-eye"></i></button>
                    <button onclick="printOrder(${order.id})" class="action-button"><i class="fas fa-print"></i></button>
                    <select onchange="updateOrderStatus(${order.id}, this.value)">
                        <option value="en_attente" ${order.statut === 'en_attente' ? 'selected' : ''}>قيد الانتظار</option>
                        <option value="payé" ${order.statut === 'payé' ? 'selected' : ''}>تم الدفع</option>
                        <option value="expédié" ${order.statut === 'expédié' ? 'selected' : ''}>تم الشحن</option>
                    </select>
                </td>
            `;
            ordersTableBody.appendChild(tr);
        });
    } catch (error) {
        console.error('Error loading orders:', error);
    }
}

// Initialize TinyMCE
function initTinyMCE() {
    tinymce.init({
        selector: '#productDescription, .block-content',
        height: 300,
        plugins: 'advlist autolink lists link image charmap preview anchor searchreplace visualblocks code fullscreen insertdatetime media table paste help wordcount',
        toolbar: 'undo redo | formatselect | bold italic underline strikethrough | alignleft aligncenter alignright alignjustify | bullist numlist outdent indent | removeformat | help',
        directionality: 'rtl',
        language: 'ar',
        content_style: "body { font-family: 'Noto Sans Arabic', sans-serif; }"
    });
}

// Initialize all components when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    checkAuth();
    notificationManager.init();
    initTinyMCE();
    loadDashboard(); // Load initial dashboard data
});

// Handle product type selection
document.getElementById('productType').addEventListener('change', (e) => {
    const productType = e.target.value;
    document.querySelectorAll('.field-group').forEach(group => {
        group.classList.remove('active');
    });
    document.getElementById(`${productType}Fields`).classList.add('active');
});

// Handle product form submission
document.getElementById('bookForm').addEventListener('submit', async (e) => {
    e.preventDefault();
    const formData = new FormData();
    const productType = document.getElementById('productType').value;

    // Common fields
    formData.append('type', productType);
    formData.append('titre', document.getElementById('productTitle').value);
    formData.append('description', document.getElementById('productDescription').value);
    formData.append('prix', document.getElementById('productPrice').value);
    formData.append('stock', document.getElementById('productStock').value);

    // Type-specific fields
    switch(productType) {
        case 'book':
            formData.append('auteur', document.getElementById('bookAuthor').value);
            break;
        case 'backpack':
            formData.append('material', document.getElementById('backpackMaterial').value);
            formData.append('capacity', document.getElementById('backpackCapacity').value);
            break;
        case 'laptop':
            formData.append('processor', document.getElementById('laptopProcessor').value);
            formData.append('ram', document.getElementById('laptopRam').value);
            formData.append('storage', document.getElementById('laptopStorage').value);
            break;
    }

    const imageFile = document.getElementById('productImage').files[0];
    if (imageFile) {
        formData.append('image', imageFile);
    }

    // Landing page data
    const landingData = productLandingManager.getFormData();
    formData.append('has_landing_page', landingData.hasLandingPage);
    formData.append('landing_page_enabled', landingData.landingPageEnabled);

    // Append content blocks
    landingData.contentBlocks.forEach((block, index) => {
        formData.append(`content_blocks[${index}][title]`, block.title);
        formData.append(`content_blocks[${index}][content]`, block.content);
        formData.append(`content_blocks[${index}][sort_order]`, block.sortOrder);
    });

    // Append gallery images
    landingData.galleryImages.forEach((file, index) => {
        formData.append(`gallery_images[${index}]`, file);
    });

    const bookId = this.getAttribute('data-book-id');
    const method = bookId ? 'PUT' : 'POST';
    const url = '../php/books.php' + (bookId ? `?id=${bookId}` : '');

    fetch(url, {
        method: method,
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            closeModal('bookModal');
            loadBooks();
        }
    });
});

// Fonctions utilitaires
function getStatusText(status) {
    const statusMap = {
        'en_attente': 'قيد الانتظار',
        'payé': 'تم الدفع',
        'expédié': 'تم الشحن'
    };
    return statusMap[status] || status;
}

function showModal(modalId) {
    document.getElementById(modalId).style.display = 'block';
}

function closeModal(modalId) {
    document.getElementById(modalId).style.display = 'none';
}

function logout() {
    fetch('../php/admin.php?action=logout', {
        method: 'POST'
    })
    .then(() => {
        window.location.href = 'login.html';
    });
}

// Gestionnaires d'événements pour les modals
document.querySelectorAll('.close').forEach(closeBtn => {
    closeBtn.addEventListener('click', function() {
        this.closest('.modal').style.display = 'none';
    });
});

document.getElementById('addBookBtn').addEventListener('click', function() {
    document.getElementById('bookForm').reset();
    document.getElementById('bookForm').removeAttribute('data-book-id');
    document.getElementById('modalTitle').textContent = 'إضافة كتاب جديد';
    showModal('bookModal');
});

// Handle store settings form
document.getElementById('storeSettingsForm').addEventListener('submit', async (e) => {
    e.preventDefault();
    const formData = {
        store_name: document.getElementById('storeName').value,
        store_phone: document.getElementById('storePhone').value,
        store_email: document.getElementById('storeEmail').value,
        store_address: document.getElementById('storeAddress').value
    };

    try {
        const response = await fetch('../php/admin.php?action=update_store_settings', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(formData)
        });

        const data = await response.json();
        if (data.success) {
            alert('تم حفظ إعدادات المتجر بنجاح');
        } else {
            alert(data.error || 'حدث خطأ أثناء حفظ إعدادات المتجر');
        }
    } catch (error) {
        console.error('Error:', error);
        alert('حدث خطأ أثناء حفظ إعدادات المتجر');
    }
});

// Load store settings on page load
async function loadStoreSettings() {
    try {
        const response = await fetch('../php/admin.php?action=get_store_settings');
        const data = await response.json();
        if (data.success) {
            document.getElementById('storeName').value = data.settings.store_name || '';
            document.getElementById('storePhone').value = data.settings.store_phone || '';
            document.getElementById('storeEmail').value = data.settings.store_email || '';
            document.getElementById('storeAddress').value = data.settings.store_address || '';
        }
    } catch (error) {
        console.error('Error loading store settings:', error);
    }
}

// Initialisation de TinyMCE
function initTinyMCE() {
    tinymce.init({
        selector: '#productDescription, .block-content',
        plugins: 'advlist autolink lists link image charmap print preview anchor searchreplace visualblocks code fullscreen insertdatetime media table paste help wordcount',
        toolbar: 'undo redo | formatselect | bold italic backcolor | alignleft aligncenter alignright alignjustify | bullist numlist outdent indent | removeformat | help',
        height: 300,
        directionality: 'rtl',
        language: 'ar',
        content_style: 'body { font-family: "Noto Sans Arabic", sans-serif; }'
    });
}

// Initialisation
document.addEventListener('DOMContentLoaded', function() {
    checkAuth();
    loadDashboard();
    loadStoreSettings();
    notificationManager.init();
    initTinyMCE();
});