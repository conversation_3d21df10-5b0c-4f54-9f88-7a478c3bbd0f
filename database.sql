-- Création de la base de données avec support UTF-8
CREATE DATABASE IF NOT EXISTS `Mossab-landing-page` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE `Mossab-landing-page`;

-- Table des livres
CREATE TABLE IF NOT EXISTS `livres` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `titre` VARCHAR(255) NOT NULL,
    `auteur` VARCHAR(255) NOT NULL,
    `description` TEXT,
    `prix` DECIMAL(10,2) NOT NULL,
    `image_url` VARCHAR(255),
    `stock` INT DEFAULT 0,
    `date_ajout` DATETIME DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Table des utilisateurs (optionnelle)
CREATE TABLE IF NOT EXISTS `utilisateurs` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `nom` VARCHAR(100),
    `email` VARCHAR(150) UNIQUE,
    `mot_de_passe` VARCHAR(255),
    `date_creation` DATETIME DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Table des administrateurs
CREATE TABLE IF NOT EXISTS `admins` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `nom_utilisateur` VARCHAR(50) UNIQUE NOT NULL,
    `mot_de_passe` VARCHAR(255) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Table du panier
CREATE TABLE IF NOT EXISTS `panier` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `session_id` VARCHAR(255),
    `livre_id` INT,
    `quantite` INT DEFAULT 1,
    `date_ajout` DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (`livre_id`) REFERENCES `livres`(`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Table des commandes
CREATE TABLE IF NOT EXISTS `commandes` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `session_id` VARCHAR(255),
    `nom_client` VARCHAR(255),
    `telephone` VARCHAR(50),
    `adresse_livraison` TEXT,
    `montant_total` DECIMAL(10,2),
    `statut` ENUM('en_attente', 'payé', 'expédié') DEFAULT 'en_attente',
    `date_commande` DATETIME DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Table des détails de commande
CREATE TABLE IF NOT EXISTS `details_commande` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `commande_id` INT,
    `livre_id` INT,
    `quantite` INT,
    `prix_unitaire` DECIMAL(10,2),
    FOREIGN KEY (`commande_id`) REFERENCES `commandes`(`id`) ON DELETE CASCADE,
    FOREIGN KEY (`livre_id`) REFERENCES `livres`(`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Table des reçus de paiement
CREATE TABLE IF NOT EXISTS `recus_paiement` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `commande_id` INT,
    `numero_recu` VARCHAR(100),
    `image_recu_url` VARCHAR(255),
    `date_reception` DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (`commande_id`) REFERENCES `commandes`(`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Insertion de données de démonstration
INSERT INTO `livres` (`titre`, `auteur`, `description`, `prix`, `image_url`, `stock`) VALUES
('العادات الذرية', 'جيمس كلير', 'كتاب يشرح كيفية بناء عادات جيدة والتخلص من العادات السيئة', 2500.00, 'images/book1.svg', 50),
('قوة العادات', 'تشارلز دوهيج', 'لماذا نفعل ما نفعل في الحياة والأعمال', 2200.00, 'images/book2.svg', 45),
('فن اللامبالاة', 'مارك مانسون', 'نهج معاكس للعيش حياة جيدة', 1800.00, 'images/book3.svg', 30);

-- Création d'un compte administrateur par défaut (mot de passe: admin123)
INSERT INTO `admins` (`nom_utilisateur`, `mot_de_passe`) VALUES
('admin', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi');