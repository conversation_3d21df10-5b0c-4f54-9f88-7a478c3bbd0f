// Product Landing Page Management
const productLandingManager = {
    init() {
        this.bindEvents();
    },

    bindEvents() {
        // Add landing page tab to product modal
        const productTypeSelect = document.getElementById('productType');
        const modalContent = document.querySelector('.modal-content');
        
        if (modalContent) {
            const landingTab = document.createElement('div');
            landingTab.id = 'landingPageFields';
            landingTab.className = 'field-group';
            landingTab.innerHTML = `
                <h4>صفحة المنتج</h4>
                <div class="form-group">
                    <label class="checkbox-label">
                        <input type="checkbox" id="hasLandingPage"> تفعيل صفحة المنتج
                    </label>
                </div>
                <div class="form-group">
                    <label class="checkbox-label">
                        <input type="checkbox" id="landingPageEnabled"> عرض صفحة المنتج
                    </label>
                </div>
                <div id="contentBlocks"></div>
                <button type="button" class="action-button" id="addContentBlock">
                    <i class="fas fa-plus"></i> إضافة قسم جديد
                </button>
                <div class="form-group">
                    <label>معرض الصور</label>
                    <input type="file" id="galleryImages" multiple accept="image/*" class="file-input">
                    <div id="imagePreview" class="image-preview"></div>
                </div>
            `;
            
            modalContent.querySelector('form').appendChild(landingTab);
            
            // Initialize content blocks
            this.initContentBlocks();
            this.initImageGallery();
        }
    },

    initContentBlocks() {
        const addBlockBtn = document.getElementById('addContentBlock');
        const contentBlocks = document.getElementById('contentBlocks');
        
        if (addBlockBtn && contentBlocks) {
            addBlockBtn.addEventListener('click', () => {
                const blockId = Date.now();
                const blockHtml = `
                    <div class="content-block" data-block-id="${blockId}">
                        <div class="block-header">
                            <input type="text" class="block-title" placeholder="عنوان القسم">
                            <button type="button" class="remove-block">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                        <textarea class="block-content" rows="5" placeholder="محتوى القسم"></textarea>
                    </div>
                `;
                
                contentBlocks.insertAdjacentHTML('beforeend', blockHtml);
                
                // Initialize rich text editor if available
                const textarea = contentBlocks.querySelector(`[data-block-id="${blockId}"] textarea`);
                if (window.tinymce) {
                    tinymce.init({
                        selector: `[data-block-id="${blockId}"] textarea`,
                        directionality: 'rtl',
                        language: 'ar',
                        plugins: 'link lists image',
                        toolbar: 'undo redo | formatselect | bold italic | alignright aligncenter alignleft | bullist numlist | link image'
                    });
                }
                
                // Remove block handler
                const removeBtn = contentBlocks.querySelector(`[data-block-id="${blockId}"] .remove-block`);
                removeBtn.addEventListener('click', (e) => {
                    const block = e.target.closest('.content-block');
                    if (window.tinymce) {
                        const editor = tinymce.get(textarea.id);
                        if (editor) {
                            editor.remove();
                        }
                    }
                    block.remove();
                });
            });
        }
    },

    initImageGallery() {
        const galleryInput = document.getElementById('galleryImages');
        const preview = document.getElementById('imagePreview');
        
        if (galleryInput && preview) {
            galleryInput.addEventListener('change', (e) => {
                preview.innerHTML = '';
                const files = Array.from(e.target.files);
                
                files.forEach(file => {
                    if (file.type.startsWith('image/')) {
                        const reader = new FileReader();
                        reader.onload = (e) => {
                            const img = document.createElement('div');
                            img.className = 'preview-image';
                            img.style.backgroundImage = `url(${e.target.result})`;
                            
                            const removeBtn = document.createElement('button');
                            removeBtn.className = 'remove-image';
                            removeBtn.innerHTML = '<i class="fas fa-times"></i>';
                            removeBtn.onclick = () => img.remove();
                            
                            img.appendChild(removeBtn);
                            preview.appendChild(img);
                        };
                        reader.readAsDataURL(file);
                    }
                });
            });
        }
    },

    getFormData() {
        const data = {
            hasLandingPage: document.getElementById('hasLandingPage').checked,
            landingPageEnabled: document.getElementById('landingPageEnabled').checked,
            contentBlocks: [],
            galleryImages: []
        };
        
        // Get content blocks
        document.querySelectorAll('.content-block').forEach((block, index) => {
            const blockData = {
                title: block.querySelector('.block-title').value,
                content: window.tinymce ? 
                    tinymce.get(block.querySelector('textarea').id).getContent() : 
                    block.querySelector('textarea').value,
                sortOrder: index
            };
            data.contentBlocks.push(blockData);
        });
        
        // Get gallery images
        const galleryInput = document.getElementById('galleryImages');
        if (galleryInput.files.length > 0) {
            data.galleryImages = Array.from(galleryInput.files);
        }
        
        return data;
    },

    setFormData(data) {
        if (!data) return;
        
        document.getElementById('hasLandingPage').checked = data.hasLandingPage;
        document.getElementById('landingPageEnabled').checked = data.landingPageEnabled;
        
        // Set content blocks
        const contentBlocks = document.getElementById('contentBlocks');
        contentBlocks.innerHTML = '';
        
        if (data.contentBlocks) {
            data.contentBlocks.forEach(block => {
                // Add block and set its content
                document.getElementById('addContentBlock').click();
                const lastBlock = contentBlocks.lastElementChild;
                
                lastBlock.querySelector('.block-title').value = block.title;
                if (window.tinymce) {
                    const editor = tinymce.get(lastBlock.querySelector('textarea').id);
                    if (editor) {
                        editor.setContent(block.content);
                    }
                } else {
                    lastBlock.querySelector('textarea').value = block.content;
                }
            });
        }
        
        // Set gallery preview
        if (data.galleryImages) {
            const preview = document.getElementById('imagePreview');
            preview.innerHTML = '';
            
            data.galleryImages.forEach(image => {
                const img = document.createElement('div');
                img.className = 'preview-image';
                img.style.backgroundImage = `url(${image})`;
                
                const removeBtn = document.createElement('button');
                removeBtn.className = 'remove-image';
                removeBtn.innerHTML = '<i class="fas fa-times"></i>';
                removeBtn.onclick = () => img.remove();
                
                img.appendChild(removeBtn);
                preview.appendChild(img);
            });
        }
    }
};

// Initialize when document is ready
document.addEventListener('DOMContentLoaded', () => {
    productLandingManager.init();
});