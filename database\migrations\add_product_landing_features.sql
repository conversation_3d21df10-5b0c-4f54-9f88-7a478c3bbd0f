-- Add slug and landing page fields to products table
ALTER TABLE `livres` 
ADD COLUMN `slug` VARCHAR(255) UNIQUE AFTER `titre`,
ADD COLUMN `has_landing_page` BO<PERSON>EAN DEFAULT FALSE,
ADD COLUMN `landing_page_enabled` BOOLEAN DEFAULT FALSE;

-- Create table for product galleries
CREATE TABLE IF NOT EXISTS `product_images` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `product_id` INT NOT NULL,
    `image_url` VARCHAR(255) NOT NULL,
    `sort_order` INT DEFAULT 0,
    `created_at` DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (`product_id`) REFERENCES `livres`(`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create table for rich content blocks
CREATE TABLE IF NOT EXISTS `product_content_blocks` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `product_id` INT NOT NULL,
    `title` VARCHAR(255),
    `content` TEXT NOT NULL,
    `sort_order` INT DEFAULT 0,
    `created_at` DATETIME DEFAULT CURRENT_TIMESTAMP,
    `updated_at` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (`product_id`) REFERENCES `livres`(`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Add indexes for performance
CREATE INDEX idx_product_slug ON `livres` (`slug`);
CREATE INDEX idx_product_landing ON `livres` (`has_landing_page`, `landing_page_enabled`);