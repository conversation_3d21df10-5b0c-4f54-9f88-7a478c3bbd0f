# Activer le moteur de réécriture
RewriteEngine On

# Définir l'encodage par défaut
AddDefaultCharset UTF-8

# Forcer HTTPS
RewriteCond %{HTTPS} off
RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]

# URLs des pages de produits
RewriteRule ^product/([^/]+)/?$ product-landing.php?slug=$1 [L,QSA]

# Protection du répertoire admin
<FilesMatch "^(admin|config)\.php$">
    Order Deny,Allow
    Deny from all
</FilesMatch>

# Protection des fichiers sensibles
<FilesMatch "^\.(htaccess|htpasswd|ini|log|sh|inc|bak|git)$">
    Order Allow,Deny
    Deny from all
</FilesMatch>

# Désactiver l'affichage du contenu des répertoires
Options -Indexes

# Headers de sécurité
<IfModule mod_headers.c>
    # Protection contre le clickjacking
    Header set X-Frame-Options "SAMEORIGIN"
    
    # Protection XSS
    Header set X-XSS-Protection "1; mode=block"
    
    # Désactiver la détection automatique du type MIME
    Header set X-Content-Type-Options "nosniff"
    
    # Référer Policy
    Header set Referrer-Policy "same-origin"
    
    # Content Security Policy
    Header set Content-Security-Policy "default-src 'self' https: data: 'unsafe-inline' 'unsafe-eval';"
</IfModule>

# Compression GZIP
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
</IfModule>

# Mise en cache des fichiers statiques
<IfModule mod_expires.c>
    ExpiresActive On
    ExpiresByType image/jpg "access plus 1 year"
    ExpiresByType image/jpeg "access plus 1 year"
    ExpiresByType image/gif "access plus 1 year"
    ExpiresByType image/png "access plus 1 year"
    ExpiresByType image/svg+xml "access plus 1 year"
    ExpiresByType text/css "access plus 1 month"
    ExpiresByType application/javascript "access plus 1 month"
</IfModule>

# Protection contre les injections SQL et XSS
<IfModule mod_rewrite.c>
    RewriteCond %{QUERY_STRING} ([^\w\s\d:/@.\-]) [NC,OR]
    RewriteCond %{QUERY_STRING} (\<|%3C).*script.*(\>|%3E) [NC,OR]
    RewriteCond %{QUERY_STRING} GLOBALS(=|\[|\%[0-9A-Z]{0,2}) [OR]
    RewriteCond %{QUERY_STRING} _REQUEST(=|\[|\%[0-9A-Z]{0,2})
    RewriteRule ^(.*)$ index.php [F,L]
</IfModule>

# Redirection des erreurs
ErrorDocument 400 /error.html
ErrorDocument 401 /error.html
ErrorDocument 403 /error.html
ErrorDocument 404 /error.html
ErrorDocument 500 /error.html