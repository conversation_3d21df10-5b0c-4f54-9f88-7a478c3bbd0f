/* Styles généraux de l'admin */
.admin-container {
    display: grid;
    grid-template-columns: 250px 1fr;
    min-height: 100vh;
}

@media (max-width: 768px) {
    .admin-container {
        grid-template-columns: 1fr;
    }

    .sidebar {
        width: 100%;
        height: auto;
        position: relative;
        padding: 10px 0;
    }

    .main-content {
        margin-right: 0;
        padding: 15px;
    }

    .stats-grid {
        grid-template-columns: 1fr;
    }

    .modal-content {
        width: 95%;
        margin: 20px auto;
        padding: 15px;
    }

    .table-responsive {
        margin-top: 10px;
    }

    th, td {
        padding: 10px;
        font-size: 0.9rem;
    }
}

/* Styles de la sidebar */
.sidebar {
    background: #2c3e50;
    color: #fff;
    padding: 20px 0;
    position: fixed;
    width: 250px;
    height: 100vh;
    overflow-y: auto;
}

.sidebar .logo {
    padding: 0 20px;
    margin-bottom: 30px;
}

.sidebar .logo h1 {
    font-size: 1.5rem;
    color: #fff;
}

.admin-nav ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.sidebar nav ul li {
    padding: 15px 20px;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 10px;
    color: #fff;
    transition: background-color 0.3s;
    position: relative;
    user-select: none;
}

.notification-badge {
    position: absolute;
    top: 5px;
    left: 5px;
    background: #e74c3c;
    color: white;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    font-weight: bold;
}

.admin-nav li:hover,
.admin-nav li.active {
    background: #34495e;
    transform: translateX(-5px);
}

.admin-nav li:active {
    background-color: rgba(255, 255, 255, 0.2);
    transform: translateX(-2px);
}

.admin-nav li i {
    margin-left: 10px;
    width: 20px;
    text-align: center;
}

/* Styles du contenu principal */
.main-content {
    padding: 30px;
    margin-right: 250px;
    background: #f8f9fa;
}

.content-section {
    display: none;
}

.content-section.active {
    display: block;
}

.content-section h2 {
    color: #2c3e50;
    margin-bottom: 30px;
}

/* Styles des cartes statistiques */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.stat-card {
    background: #fff;
    padding: 20px;
    border-radius: 10px;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    display: flex;
    align-items: center;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.stat-card:active {
    transform: translateY(0);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.stat-card::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.1);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.stat-card:hover::after {
    opacity: 1;
}

.stat-card i {
    font-size: 2rem;
    color: #3498db;
    margin-left: 20px;
}

.stat-info h3 {
    color: #7f8c8d;
    font-size: 0.9rem;
    margin-bottom: 5px;
}

.stat-info p {
    color: #2c3e50;
    font-size: 1.5rem;
    font-weight: 700;
    margin: 0;
}

/* Styles des cartes de paramètres */
.settings-card {
    background: #fff;
    padding: 25px;
    border-radius: 10px;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    margin-bottom: 30px;
    max-width: 800px;
    margin-left: auto;
    margin-right: auto;
}

.settings-card h3 {
    color: #2c3e50;
    margin-bottom: 20px;
    font-size: 1.2rem;
    border-bottom: 2px solid #f1f1f1;
    padding-bottom: 10px;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    color: #2c3e50;
    font-weight: 500;
}

.form-group input,
.form-group textarea {
    width: 100%;
    padding: 12px;
    border: 1px solid #ddd;
    border-radius: 5px;
    font-size: 1rem;
    transition: all 0.3s ease;
    background-color: #f8f9fa;
}

.form-group textarea {
    min-height: 100px;
    resize: vertical;
}

.form-group input:focus,
.form-group textarea:focus {
    border-color: #3498db;
    outline: none;
    box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
    background-color: #fff;
}

.action-button {
    background: #3498db;
    color: #fff;
    border: none;
    padding: 12px 25px;
    border-radius: 5px;
    cursor: pointer;
    font-size: 1rem;
    transition: all 0.3s ease;
    display: block;
    width: 100%;
    max-width: 200px;
    margin: 20px auto 0;
    text-align: center;
}

.action-button:hover {
    background: #2980b9;
    transform: translateY(-2px);
    box-shadow: 0 2px 5px rgba(0,0,0,0.2);
}

.action-button:active {
    transform: translateY(1px);
    box-shadow: none;
}

@media (max-width: 768px) {
    .settings-card {
        padding: 15px;
        margin: 15px;
    }

    .form-group input,
    .form-group textarea {
        padding: 10px;
    }

    .action-button {
        padding: 10px 20px;
        font-size: 0.9rem;
    }
}

/* Styles des tableaux */
.table-responsive {
    overflow-x: auto;
    background: #fff;
    border-radius: 10px;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    margin-top: 20px;
}

table {
    width: 100%;
    border-collapse: collapse;
}

th, td {
    padding: 15px;
    text-align: right;
    border-bottom: 1px solid #eee;
}

th {
    background: #f8f9fa;
    color: #2c3e50;
    font-weight: 600;
}

td {
    color: #666;
}

/* Styles des boutons d'action */
.action-button {
    background: #3498db;
    color: #fff;
    border: none;
    padding: 10px 20px;
    border-radius: 5px;
    cursor: pointer;
    font-family: 'Noto Sans Arabic', sans-serif;
    display: inline-flex;
    align-items: center;
    transition: background-color 0.3s ease;
}

.action-button i {
    margin-left: 8px;
}

.action-button:hover {
    background: #2980b9;
}

/* Styles des modals */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.5);
    z-index: 1000;
    overflow-y: auto;
    padding: 20px;
}

.modal-content {
    background: #fff;
    width: 90%;
    max-width: 600px;
    margin: 20px auto;
    padding: 30px;
    border-radius: 10px;
    position: relative;
    max-height: calc(100vh - 40px);
    overflow-y: auto;
}

.close {
    position: absolute;
    top: 20px;
    left: 20px;
    font-size: 1.5rem;
    cursor: pointer;
    color: #666;
}

/* Styles des formulaires */
.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
}

.form-group input,
.form-group textarea,
.form-group select {
    width: 100%;
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-family: 'Noto Sans Arabic', sans-serif;
}

.form-group textarea {
    min-height: 100px;
    resize: vertical;
}

.product-type-select {
    margin-bottom: 15px;
}

.field-group {
    display: none;
    padding: 20px;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    margin-bottom: 20px;
}

.field-group.active {
    display: block;
}

.field-group h4 {
    color: #2c3e50;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 2px solid #f0f2f5;
}

.field-group .checkbox-label {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
    cursor: pointer;
}

.field-group .checkbox-label input[type="checkbox"] {
    width: auto;
    margin-left: 10px;
}

.content-block {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 6px;
    margin-bottom: 15px;
}

.block-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.block-title {
    flex: 1;
    margin-left: 10px;
}

.remove-block {
    background: #e74c3c;
    color: #fff;
    border: none;
    padding: 5px 10px;
    border-radius: 4px;
    cursor: pointer;
}

.remove-block:hover {
    background: #c0392b;
}

.image-preview {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
    gap: 10px;
    margin-top: 10px;
}

.preview-image {
    position: relative;
    padding-bottom: 100%;
    background-size: cover;
    background-position: center;
    border-radius: 4px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.remove-image {
    position: absolute;
    top: 5px;
    left: 5px;
    background: rgba(231, 76, 60, 0.8);
    color: #fff;
    border: none;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
}

.remove-image:hover {
    background: rgba(192, 57, 43, 0.9);
}

/* Styles des badges de statut */
.status-badge {
    padding: 5px 10px;
    border-radius: 15px;
    font-size: 0.9rem;
    font-weight: 500;
}

/* Notifications */
.notifications-container {
    position: fixed;
    top: 20px;
    left: 20px;
    z-index: 1000;
    width: 300px;
}

.notification {
    background: #fff;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
    from {
        transform: translateX(-100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

.notification-content {
    flex: 1;
    margin-left: 10px;
}

.notification-title {
    font-weight: 600;
    margin-bottom: 5px;
    color: #2c3e50;
}

.notification-message {
    color: #666;
    font-size: 0.9em;
}

.notification-close {
    background: none;
    border: none;
    color: #999;
    cursor: pointer;
    padding: 5px;
    font-size: 1.2em;
    line-height: 1;
}

.notification-close:hover {
    color: #666;
}

.notification.new-order {
    border-right: 4px solid #3498db;
}

.notification.unread {
    background-color: #f8f9fa;
}

.status-pending {
    background-color: #fef3c7;
    color: #92400e;
    border: 1px solid #f59e0b;
}

.status-paid {
    background-color: #dcfce7;
    color: #166534;
    border: 1px solid #22c55e;
}

.status-shipped {
    background-color: #dbeafe;
    color: #1e40af;
    border: 1px solid #3b82f6;
}

.orders-filters {
    display: flex;
    gap: 15px;
    margin-bottom: 20px;
    padding: 15px;
    background: #f8fafc;
    border-radius: 8px;
    border: 1px solid #e2e8f0;
}

.filter-group {
    display: flex;
    align-items: center;
    gap: 8px;
}

.filter-group label {
    font-weight: 500;
    color: #475569;
}

.filter-group select {
    padding: 6px 12px;
    border: 1px solid #cbd5e1;
    border-radius: 6px;
    background-color: white;
    color: #1e293b;
    font-size: 0.9em;
}

.orders-table {
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    border-radius: 8px;
    overflow: hidden;
}

.orders-table th {
    background-color: #f8fafc;
    color: #475569;
    font-weight: 600;
    text-transform: uppercase;
    font-size: 0.8em;
    letter-spacing: 0.05em;
}

.orders-table td {
    padding: 12px 16px;
    border-bottom: 1px solid #e2e8f0;
}

.customer-info,
.order-details,
.date-info {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.customer-info strong,
.order-details strong,
.date-info strong {
    color: #1e293b;
    font-size: 0.95em;
}

.customer-email,
.items-count,
.time {
    color: #64748b;
    font-size: 0.85em;
}

.action-button {
    padding: 8px;
    margin: 0 4px;
    border: none;
    border-radius: 6px;
    background: transparent;
    color: #475569;
    cursor: pointer;
    transition: all 0.2s ease;
}

.action-button:hover {
    background: #f1f5f9;
    color: #1e293b;
}

.action-button i {
    font-size: 1.1em;
}

.status-select {
    padding: 6px 12px;
    border: 1px solid #e2e8f0;
    border-radius: 6px;
    background-color: white;
    color: #1e293b;
    font-size: 0.9em;
    cursor: pointer;
    transition: all 0.2s ease;
}

.status-select:hover {
    border-color: #cbd5e1;
}

.status-select:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
}

.orders-table tr:last-child td {
    border-bottom: none;
}

.orders-table tr {
    transition: all 0.2s ease;
}

.orders-table tr:hover {
    background-color: #f1f5f9;
    transform: translateX(-4px);
}

.orders-table tr:active {
    background-color: #e2e8f0;
    transform: translateX(0);
}

/* Styles des filtres */
.orders-filters {
    margin-bottom: 20px;
}

.orders-filters select {
    padding: 8px 15px;
    border: 1px solid #ddd;
    border-radius: 5px;
    font-family: 'Noto Sans Arabic', sans-serif;
}



/* Responsive Design */
@media (max-width: 768px) {
    .admin-container {
        grid-template-columns: 1fr;
    }

    .sidebar {
        position: fixed;
        right: -250px;
        transition: right 0.3s ease;
        z-index: 1000;
    }

    .sidebar.active {
        right: 0;
    }

    .main-content {
        margin-right: 0;
        padding: 20px;
    }

    .stats-grid {
        grid-template-columns: 1fr;
    }

    .modal-content {
        width: 95%;
        margin: 20px auto;
        padding: 20px;
    }
}
