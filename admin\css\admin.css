:root {
    --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --card-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    --border-radius-lg: 16px;
    --spacing-lg: 30px;
    --transition-base: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --color-text-primary: #1e293b;
    --color-text-secondary: #64748b;
    --color-border: #e2e8f0;
    --color-background: #f8fafc;
}

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Noto Sans Arabic', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background-color: #f8fafc;
    color: #1e293b;
    line-height: 1.6;
    direction: rtl;
}

/* Admin Container */
.admin-container {
    display: flex;
    min-height: 100vh;
    background-color: #f8fafc;
}

/* Mobile First Responsive Design */
@media (max-width: 768px) {
    .admin-container {
        flex-direction: column;
    }

    .sidebar {
        position: fixed;
        top: 0;
        right: -280px;
        width: 280px;
        height: 100vh;
        z-index: 1000;
        transition: right 0.3s ease;
        box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
    }

    .sidebar.mobile-open {
        right: 0;
    }

    .main-content {
        margin-right: 0;
        padding: 20px 15px;
        width: 100%;
    }

    .mobile-menu-toggle {
        display: block;
        position: fixed;
        top: 20px;
        right: 20px;
        z-index: 1001;
        background: #3b82f6;
        color: white;
        border: none;
        padding: 12px;
        border-radius: 8px;
        cursor: pointer;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    }
}

/* Sidebar Styles */
.sidebar {
    width: 280px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: #ffffff;
    padding: 0;
    position: fixed;
    height: 100vh;
    overflow-y: auto;
    box-shadow: 2px 0 20px rgba(0, 0, 0, 0.1);
    z-index: 100;
}

.sidebar .logo {
    padding: 30px 25px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    background: rgba(255, 255, 255, 0.05);
}

.sidebar .logo h1 {
    font-size: 1.75rem;
    font-weight: 700;
    color: #ffffff;
    text-align: center;
    margin: 0;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.admin-nav {
    padding: 20px 0;
}

.admin-nav ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.admin-nav ul li {
    margin: 8px 15px;
    padding: 15px 20px;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 15px;
    color: rgba(255, 255, 255, 0.9);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    border-radius: 12px;
    position: relative;
    -webkit-user-select: none;
    user-select: none;
    font-weight: 500;
}

.admin-nav ul li:hover {
    background: rgba(255, 255, 255, 0.15);
    color: #ffffff;
    transform: translateX(-3px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.admin-nav ul li.active {
    background: rgba(255, 255, 255, 0.2);
    color: #ffffff;
    transform: translateX(-3px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.admin-nav ul li.active::before {
    content: '';
    position: absolute;
    right: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 4px;
    height: 60%;
    background: #ffffff;
    border-radius: 2px 0 0 2px;
}

.admin-nav ul li i {
    font-size: 1.1rem;
    width: 24px;
    text-align: center;
    opacity: 0.9;
}

.admin-nav ul li span {
    font-size: 0.95rem;
    font-weight: 500;
}

.mobile-menu-toggle {
    display: none;
}

/* Main Content Styles */
.main-content {
    flex: 1;
    margin-right: 280px;
    padding: 40px;
    background: #f8fafc;
    min-height: 100vh;
    transition: margin-right 0.3s ease;
}

.content-section {
    display: none;
    opacity: 0;
    transition: opacity 0.3s ease-in-out;
}

.content-section.active {
    display: block;
    opacity: 1;
}

.content-section h2 {
    color: #1e293b;
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 30px;
    padding-bottom: 15px;
}

/* View More Link Styles */
.view-more-link {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    margin-right: 8px;
    background-color: #3b82f6;
    color: white;
    border-radius: 6px;
    text-decoration: none;
    transition: all 0.3s ease;
}

.view-more-link:hover {
    background-color: #2563eb;
    transform: translateY(-2px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}
    border-bottom: 3px solid #e2e8f0;
    position: relative;
}

.content-section h2::after {
    content: '';
    position: absolute;
    bottom: -3px;
    right: 0;
    width: 60px;
    height: 3px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 2px;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Stats Grid and Cards */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 25px;
    margin-bottom: 40px;
}

.stat-card {
    background: #ffffff;
    padding: 30px;
    border-radius: 16px;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    display: flex;
    align-items: center;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
    border: 1px solid #f1f5f9;
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 4px;
    height: 100%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    transform: scaleY(0);
    transition: transform 0.3s ease;
}

.stat-card:hover::before {
    transform: scaleY(1);
}

.stat-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    border-color: #e2e8f0;
}

.stat-card:active {
    transform: translateY(-2px);
}

.stat-card i {
    font-size: 2.5rem;
    margin-left: 25px;
    padding: 20px;
    border-radius: 12px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: #ffffff;
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

.stat-info {
    flex: 1;
}

.stat-info h3 {
    color: #64748b;
    font-size: 0.875rem;
    font-weight: 500;
    margin-bottom: 8px;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.stat-info p {
    color: #1e293b;
    font-size: 2rem;
    font-weight: 700;
    margin: 0;
    line-height: 1;
}

/* Settings Section Styles */
#settings.content-section {
    max-width: 1000px;
    margin: 0 auto;
    display: flex;
    flex-direction: column;
    gap: 30px;
}

.settings-card {
    background: #ffffff;
    border-radius: 16px;
    padding: 35px;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    border: 1px solid #f1f5f9;
    position: relative;
    overflow: hidden;
}

.settings-card::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 100%;
    height: 4px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.settings-card:hover {
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    transform: translateY(-2px);
}

.settings-card h3 {
    margin-bottom: 30px;
    color: #1e293b;
    font-size: 1.5rem;
    font-weight: 700;
    text-align: right;
    position: relative;
    padding-bottom: 15px;
}

.settings-card h3::after {
    content: '';
    position: absolute;
    bottom: 0;
    right: 0;
    width: 50px;
    height: 3px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 2px;
}

/* Form Styles */
.form-group {
    margin-bottom: 25px;
    display: flex;
    flex-direction: column;
    gap: 8px;
}

@media (min-width: 768px) {
    .form-group {
        display: grid;
        grid-template-columns: 200px 1fr;
        align-items: start;
        gap: 25px;
        margin-bottom: 30px;
    }
}

.form-group label {
    display: block;
    color: #374151;
    font-weight: 600;
    font-size: 0.95rem;
    text-align: right;
    margin-bottom: 0;
    padding-top: 12px;
}

.form-group input,
.form-group textarea,
.form-group select {
    width: 100%;
    padding: 14px 16px;
    border: 2px solid #e5e7eb;
    border-radius: 12px;
    font-size: 1rem;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    background-color: #ffffff;
    font-family: 'Noto Sans Arabic', sans-serif;
    text-align: right;
    outline: none;
    color: #1f2937;
}

.form-group input:hover,
.form-group textarea:hover,
.form-group select:hover {
    border-color: #d1d5db;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
}

.form-group input:focus,
.form-group textarea:focus,
.form-group select:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    background-color: #ffffff;
}

.form-group textarea {
    min-height: 120px;
    resize: vertical;
    line-height: 1.6;
}

.form-group input[type="file"] {
    padding: 12px;
    cursor: pointer;
    border-style: dashed;
    background-color: #f9fafb;
}

.form-group input[type="file"]::-webkit-file-upload-button {
    margin-left: 12px;
    padding: 10px 20px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-family: 'Noto Sans Arabic', sans-serif;
    font-weight: 500;
    transition: all 0.3s ease;
}

.form-group input[type="file"]::-webkit-file-upload-button:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

/* Action Buttons */
.action-button {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: #ffffff;
    border: none;
    padding: 14px 28px;
    border-radius: 12px;
    cursor: pointer;
    font-size: 1rem;
    font-weight: 600;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    text-decoration: none;
    font-family: 'Noto Sans Arabic', sans-serif;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    position: relative;
    overflow: hidden;
}

.action-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, #764ba2 0%, #667eea 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.action-button:hover::before {
    opacity: 1;
}

.action-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

.action-button:active {
    transform: translateY(0);
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.action-button i,
.action-button span {
    position: relative;
    z-index: 1;
}

.action-button i {
    font-size: 1.1rem;
}

/* Settings specific button styles */
.settings-card .action-button {
    width: 100%;
    max-width: 250px;
    margin: 20px auto 0;
}

/* Image Placeholder Styles */
.image-placeholder {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #f3f4f6 0%, #e5e7eb 100%);
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #9ca3af;
    font-size: 1.5rem;
    border: 2px dashed #d1d5db;
}

.product-image {
    width: 60px;
    height: 60px;
    object-fit: cover;
    border-radius: 8px;
    border: 1px solid #e5e7eb;
}

.product-image:error,
.product-image[src=""],
.product-image[src*="default-"] {
    display: none;
}

.product-image:error + .image-placeholder,
.product-image[src=""] + .image-placeholder,
.product-image[src*="default-"] + .image-placeholder {
    display: flex;
}

/* Mobile Responsive Styles */
@media (max-width: 768px) {
    .mobile-menu-toggle {
        display: block;
    }

    .main-content {
        margin-right: 0;
        padding: 20px 15px;
    }

    .stats-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .stat-card {
        padding: 25px;
    }

    .stat-card i {
        font-size: 2rem;
        padding: 15px;
        margin-left: 20px;
    }

    .stat-info p {
        font-size: 1.75rem;
    }

    #settings.content-section {
        gap: 25px;
    }

    .settings-card {
        padding: 25px 20px;
        margin: 0;
        border-radius: 12px;
    }

    .form-group {
        display: flex;
        flex-direction: column;
        gap: 8px;
        margin-bottom: 20px;
    }

    .form-group label {
        padding-top: 0;
        text-align: right;
    }

    .form-group input,
    .form-group textarea,
    .form-group select {
        padding: 12px 14px;
        font-size: 1rem;
    }

    .settings-card .action-button {
        padding: 14px 24px;
        font-size: 1rem;
        max-width: 100%;
    }

    .content-section h2 {
        font-size: 1.75rem;
        margin-bottom: 25px;
    }
}

/* Table Styles */
.table-responsive {
    overflow-x: auto;
    background: #ffffff;
    border-radius: 16px;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    margin-top: 30px;
    border: 1px solid #f1f5f9;
}

table {
    width: 100%;
    border-collapse: collapse;
}

th, td {
    padding: 18px 20px;
    text-align: right;
    border-bottom: 1px solid #f1f5f9;
}

th {
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    color: #374151;
    font-weight: 600;
    font-size: 0.875rem;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    position: sticky;
    top: 0;
    z-index: 10;
}

td {
    color: #6b7280;
    font-size: 0.95rem;
    transition: all 0.2s ease;
}

tbody tr {
    transition: all 0.2s ease;
}

tbody tr:hover {
    background-color: #f9fafb;
    transform: translateX(-2px);
}

tbody tr:hover td {
    color: #374151;
}

tbody tr:last-child td {
    border-bottom: none;
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.6);
    z-index: 1000;
    overflow-y: auto;
    padding: 20px;
    -webkit-backdrop-filter: blur(4px);
    backdrop-filter: blur(4px);
}

.modal-content {
    background: #ffffff;
    width: 90%;
    max-width: 800px;
    margin: 40px auto;
    padding: 40px;
    border-radius: 20px;
    position: relative;
    max-height: calc(100vh - 80px);
    overflow-y: auto;
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
    border: 1px solid #f1f5f9;
    animation: modalSlideIn 0.3s ease-out;
}

.product-modal {
    margin: 2% auto;
    max-height: 90vh;
    overflow-y: auto;
}

.product-modal::-webkit-scrollbar {
    width: 8px;
}

.product-modal::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

.product-modal::-webkit-scrollbar-thumb {
    background: #888;
    border-radius: 4px;
}

.product-form .field-group {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 8px;
    margin: 15px 0;
    border: 1px solid #e0e0e0;
    display: none;
}

#landingPageFields {
    border: none;
    background: transparent;
    padding: 0;
}

#landingPageFields h4 {
    font-size: 1.2em;
    color: #2196f3;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 2px solid #e0e0e0;
}

.checkbox-label {
    display: flex;
    align-items: center;
    gap: 8px;
    cursor: pointer;
    padding: 8px;
    border-radius: 4px;
    transition: background-color 0.2s;
}

.checkbox-label:hover {
    background-color: #f0f0f0;
}

.checkbox-label input[type="checkbox"] {
    width: 18px;
    height: 18px;
}

.content-block {
    background: white;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    margin: 15px 0;
    padding: 15px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.block-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.block-title {
    flex: 1;
    margin-left: 10px;
    padding: 8px;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    font-size: 1em;
}

.remove-block {
    background: #ff5252;
    color: white;
    border: none;
    border-radius: 4px;
    padding: 8px;
    cursor: pointer;
    transition: background-color 0.2s;
}

.remove-block:hover {
    background: #ff1744;
}

.image-preview {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    gap: 15px;
    margin-top: 15px;
}

.preview-image {
    position: relative;
    padding-bottom: 100%;
    background-size: cover;
    background-position: center;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.remove-image {
    position: absolute;
    top: 5px;
    right: 5px;
    background: rgba(255,255,255,0.9);
    border: none;
    border-radius: 50%;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
}

.remove-image:hover {
    background: rgba(255, 82, 82, 0.9);
    color: white;
}

/* Share Buttons */
.share-buttons {
    display: flex;
    gap: 8px;
    align-items: center;
}

.share-button {
    width: 32px;
    height: 32px;
    border: none;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 14px;
}

.share-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.2);
}

.share-button.facebook {
    background: #1877f2;
}

.share-button.whatsapp {
    background: #25d366;
}

.share-button.twitter {
    background: #1da1f2;
}

.share-button.copy {
    background: #6c757d;
}

.share-button.copy.copied {
    background: #28a745;
}

.view-button {
    width: 32px;
    height: 32px;
    border: none;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    background: #3b82f6;
    cursor: pointer;
    transition: all 0.2s ease;
    text-decoration: none;
}

.view-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.2);
    background: #2563eb;
}

/* Landing Pages Table */
#landingPagesTable .preview-cell {
    width: 100px;
}

#landingPagesTable .preview-image {
    width: 80px;
    height: 80px;
    border-radius: 8px;
    object-fit: cover;
}

#landingPagesTable .actions-cell {
    display: flex;
    gap: 8px;
    justify-content: flex-end;
}

.edit-button, .delete-button {
    width: 32px;
    height: 32px;
    border: none;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
}

.edit-button {
    background: #fbbf24;
    color: white;
}

.delete-button {
    background: #ef4444;
    color: white;
}

.edit-button:hover, .delete-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.2);
}
    cursor: pointer;
    color: #ff5252;
    transition: all 0.2s;
}

.remove-image:hover {
    background: #ff5252;
    color: white;
}

.share-buttons {
    display: flex;
    gap: 10px;
    margin-top: 10px;
}

.share-button {
    display: inline-flex;
    align-items: center;
    gap: 5px;
    padding: 5px 10px;
    border-radius: 4px;
    color: white;
    cursor: pointer;
    transition: opacity 0.2s;
    font-size: 0.9em;
}

.share-button:hover {
    opacity: 0.9;
}

.share-button.facebook {
    background-color: #1877f2;
}

.share-button.twitter {
    background-color: #1da1f2;
}

.share-button.whatsapp {
    background-color: #25d366;
}

.share-button.copy {
    background-color: #6c757d;
}

.share-button i {
    font-size: 1.1em;
}

.share-buttons {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-top: 10px;
}

.share-button {
    display: inline-flex;
    align-items: center;
    gap: 6px;
    padding: 6px 12px;
    border-radius: 4px;
    color: white;
    border: none;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 0.9em;
    min-width: 100px;
    justify-content: center;
}

.share-button:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.share-button:active {
    transform: translateY(0);
    box-shadow: none;
}

.product-form .field-group.active {
    display: block;
    animation: fadeIn 0.3s ease-in-out;
}

.product-type-select {
    width: 100%;
    padding: 10px;
    border: 2px solid #e0e0e0;
    border-radius: 6px;
    font-size: 16px;
    margin-bottom: 20px;
    background-color: white;
    transition: all 0.3s ease;
}

.product-type-select:hover {
    border-color: #4a90e2;
}

.product-type-select:focus {
    border-color: #2196f3;
    box-shadow: 0 0 0 2px rgba(33,150,243,0.1);
    outline: none;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-20px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.close {
    position: absolute;
    top: 20px;
    left: 20px;
    font-size: 1.5rem;
    cursor: pointer;
    color: #9ca3af;
    transition: all 0.3s ease;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    background: #f3f4f6;
}

.close:hover {
    color: #ef4444;
    background: #fee2e2;
    transform: scale(1.1);
}

.modal-content h3 {
    color: #1e293b;
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 30px;
    text-align: right;
    padding-right: 60px;
}

/* Styles des formulaires */
.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
}

.form-group input,
.form-group textarea,
.form-group select {
    width: 100%;
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-family: 'Noto Sans Arabic', sans-serif;
}

.form-group textarea {
    min-height: 100px;
    resize: vertical;
}

.product-type-select {
    margin-bottom: 15px;
}

.field-group {
    display: none;
    padding: 20px;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    margin-bottom: 20px;
    opacity: 0;
    transition: opacity 0.3s ease-in-out;
}

.field-group.active {
    display: block;
    opacity: 1;
}

.field-group h4 {
    color: #2c3e50;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 2px solid #f0f2f5;
}

.field-group .checkbox-label {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
    cursor: pointer;
}

.field-group .checkbox-label input[type="checkbox"] {
    width: auto;
    margin-left: 10px;
}

.content-block {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 6px;
    margin-bottom: 15px;
}

.block-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.block-title {
    flex: 1;
    margin-left: 10px;
}

.remove-block {
    background: #e74c3c;
    color: #fff;
    border: none;
    padding: 5px 10px;
    border-radius: 4px;
    cursor: pointer;
}

.remove-block:hover {
    background: #c0392b;
}

.image-preview {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
    gap: 10px;
    margin-top: 10px;
}

.preview-image {
    position: relative;
    padding-bottom: 100%;
    background-size: cover;
    background-position: center;
    border-radius: 4px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.remove-image {
    position: absolute;
    top: 5px;
    left: 5px;
    background: rgba(231, 76, 60, 0.8);
    color: #fff;
    border: none;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
}

.remove-image:hover {
    background: rgba(192, 57, 43, 0.9);
}

/* Status badges */
.status-badge {
    padding: 6px 12px;
    border-radius: 4px;
    font-size: 0.9em;
    font-weight: 500;
    display: inline-block;
}

.status-active {
    background-color: #27ae60;
    color: white;
}

.status-inactive {
    background-color: #95a5a6;
    color: white;
}

/* Notifications */
.notifications-container {
    position: fixed;
    top: 20px;
    left: 20px;
    z-index: 1000;
    width: 300px;
}

.notification {
    background: #fff;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
    from {
        transform: translateX(-100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

.notification-content {
    flex: 1;
    margin-left: 10px;
}

.notification-title {
    font-weight: 600;
    margin-bottom: 5px;
    color: #2c3e50;
}

.notification-message {
    color: #666;
    font-size: 0.9em;
}

.notification-close {
    background: none;
    border: none;
    color: #999;
    cursor: pointer;
    padding: 5px;
    font-size: 1.2em;
    line-height: 1;
}

.notification-close:hover {
    color: #666;
}

.notification.new-order {
    border-right: 4px solid #3498db;
}

.notification.unread {
    background-color: #f8f9fa;
}

.status-pending {
    background-color: #fef3c7;
    color: #92400e;
    border: 1px solid #f59e0b;
}

.status-paid {
    background-color: #dcfce7;
    color: #166534;
    border: 1px solid #22c55e;
}

.status-shipped {
    background-color: #dbeafe;
    color: #1e40af;
    border: 1px solid #3b82f6;
}

.orders-filters {
    display: flex;
    gap: 15px;
    margin-bottom: 20px;
    padding: 15px;
    background: #f8fafc;
    border-radius: 8px;
    border: 1px solid #e2e8f0;
}

.filter-group {
    display: flex;
    align-items: center;
    gap: 8px;
}

.filter-group label {
    font-weight: 500;
    color: #475569;
}

.filter-group select {
    padding: 6px 12px;
    border: 1px solid #cbd5e1;
    border-radius: 6px;
    background-color: white;
    color: #1e293b;
    font-size: 0.9em;
}

.orders-table {
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    border-radius: 8px;
    overflow: hidden;
}

.orders-table th {
    background-color: #f8fafc;
    color: #475569;
    font-weight: 600;
    text-transform: uppercase;
    font-size: 0.8em;
    letter-spacing: 0.05em;
}

.orders-table td {
    padding: 12px 16px;
    border-bottom: 1px solid #e2e8f0;
}

.customer-info,
.order-details,
.date-info {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.customer-info strong,
.order-details strong,
.date-info strong {
    color: #1e293b;
    font-size: 0.95em;
}

.customer-email,
.items-count,
.time {
    color: #64748b;
    font-size: 0.85em;
}

.action-button {
    padding: 8px;
    margin: 0 4px;
    border: none;
    border-radius: 6px;
    background: transparent;
    color: #475569;
    cursor: pointer;
    transition: all 0.2s ease;
}

.action-button:hover {
    background: #f1f5f9;
    color: #1e293b;
}

.action-button i {
    font-size: 1.1em;
}

.status-select {
    padding: 6px 12px;
    border: 1px solid #e2e8f0;
    border-radius: 6px;
    background-color: white;
    color: #1e293b;
    font-size: 0.9em;
    cursor: pointer;
    transition: all 0.2s ease;
}

.status-select:hover {
    border-color: #cbd5e1;
}

.status-select:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
}

.orders-table tr:last-child td {
    border-bottom: none;
}

.orders-table tr {
    transition: all 0.2s ease;
}

.orders-table tr:hover {
    background-color: #f1f5f9;
    transform: translateX(-4px);
}

.orders-table tr:active {
    background-color: #e2e8f0;
    transform: translateX(0);
}

/* Styles des filtres */
.orders-filters {
    margin-bottom: 20px;
}

.orders-filters select {
    padding: 8px 15px;
    border: 1px solid #ddd;
    border-radius: 5px;
    font-family: 'Noto Sans Arabic', sans-serif;
}



/* Responsive Design */
@media (max-width: 768px) {
    .admin-container {
        grid-template-columns: 1fr;
    }

    .sidebar {
        position: fixed;
        right: -250px;
        transition: right 0.3s ease;
        z-index: 1000;
    }

    .sidebar.active {
        right: 0;
    }

    .main-content {
        margin-right: 0;
        padding: 20px;
    }

    .stats-grid {
        grid-template-columns: 1fr;
    }

    .modal-content {
        width: 95%;
        margin: 20px auto;
        padding: 20px;
    }
}
