/* Reset CSS */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

/* Base Styles */
body {
    font-family: 'Noto Sans Arabic', sans-serif;
    line-height: 1.6;
    background-color: #f8f9fa;
    color: #333;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Header Styles */
.header {
    background-color: #fff;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    position: fixed;
    width: 100%;
    top: 0;
    z-index: 1000;
}

.nav {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 0;
}

.logo h1 {
    font-size: 1.8rem;
    color: #2c3e50;
    font-weight: 700;
}

.cart-icon a {
    color: #2c3e50;
    text-decoration: none;
    font-size: 1.5rem;
    position: relative;
}

.cart-count {
    position: absolute;
    top: -8px;
    left: -8px;
    background-color: #e74c3c;
    color: #fff;
    font-size: 0.8rem;
    padding: 2px 6px;
    border-radius: 50%;
}

/* Hero Section */
.hero {
    padding: 120px 0 60px;
    text-align: center;
    background: linear-gradient(135deg, #3498db, #2c3e50);
    color: #fff;
}

.hero h2 {
    font-size: 2.5rem;
    margin-bottom: 1rem;
}

.hero p {
    font-size: 1.2rem;
    opacity: 0.9;
}

/* Books Section */
.books {
    padding: 60px 0;
}

.books-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 30px;
    padding: 20px 0;
}

.book-card {
    background: #fff;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 3px 10px rgba(0,0,0,0.1);
    transition: transform 0.3s ease;
}

.book-card:hover {
    transform: translateY(-5px);
}

.book-card img {
    width: 100%;
    height: 300px;
    object-fit: cover;
}

.book-info {
    padding: 20px;
}

.book-info h3 {
    font-size: 1.4rem;
    margin-bottom: 0.5rem;
    color: #2c3e50;
}

.author {
    color: #7f8c8d;
    margin-bottom: 0.5rem;
}

.price {
    font-size: 1.2rem;
    color: #2c3e50;
    font-weight: 700;
    margin-bottom: 1rem;
}

.buttons {
    display: flex;
    gap: 10px;
}

.add-to-cart, .buy-now {
    padding: 8px 15px;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    font-family: 'Noto Sans Arabic', sans-serif;
    font-weight: 500;
    transition: background-color 0.3s ease;
}

.add-to-cart {
    background-color: #3498db;
    color: #fff;
    flex: 1;
}

.add-to-cart:hover {
    background-color: #2980b9;
}

.buy-now {
    background-color: #2ecc71;
    color: #fff;
    flex: 1;
}

.buy-now:hover {
    background-color: #27ae60;
}

/* Footer */
.footer {
    background-color: #2c3e50;
    color: #fff;
    padding: 20px 0;
    text-align: center;
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero {
        padding: 100px 0 40px;
    }

    .hero h2 {
        font-size: 2rem;
    }

    .books-grid {
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 20px;
    }

    .book-card img {
        height: 250px;
    }

    .buttons {
        flex-direction: column;
    }
}