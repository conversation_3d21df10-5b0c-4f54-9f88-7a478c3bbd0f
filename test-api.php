<?php
// Test simple de l'API landing pages
require_once 'php/config.php';

echo "<h1>Test API Landing Pages</h1>";

try {
    // Test de connexion à la base de données
    echo "<h2>1. Test de connexion à la base de données</h2>";
    echo "Connexion : " . ($conn ? "✅ OK" : "❌ Échec") . "<br>";
    
    // Test de la requête
    echo "<h2>2. Test de la requête landing pages</h2>";
    $stmt = $conn->prepare(
        "SELECT lp.*, p.titre as product_title
         FROM landing_pages lp
         JOIN produits p ON lp.produit_id = p.id"
    );
    $stmt->execute();
    $pages = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "Nombre de pages trouvées : " . count($pages) . "<br>";
    
    if (count($pages) > 0) {
        echo "<h3>Données des pages :</h3>";
        echo "<pre>";
        print_r($pages);
        echo "</pre>";
        
        // Test des images pour chaque page
        echo "<h3>Images pour chaque page :</h3>";
        foreach ($pages as $page) {
            echo "<h4>Page ID: {$page['id']} - {$page['titre']}</h4>";
            $stmt = $conn->prepare(
                "SELECT image_url
                 FROM landing_page_images
                 WHERE landing_page_id = ?
                 ORDER BY ordre"
            );
            $stmt->execute([$page['id']]);
            $images = $stmt->fetchAll(PDO::FETCH_COLUMN);
            echo "Images: " . count($images) . "<br>";
            if (count($images) > 0) {
                echo "<pre>";
                print_r($images);
                echo "</pre>";
            }
        }
    }
    
    // Test de la réponse JSON
    echo "<h2>3. Test de la réponse JSON</h2>";
    
    // Simuler la fonction handleGet()
    foreach ($pages as &$page) {
        $stmt = $conn->prepare(
            "SELECT image_url
             FROM landing_page_images
             WHERE landing_page_id = ?
             ORDER BY ordre"
        );
        $stmt->execute([$page['id']]);
        $page['images'] = $stmt->fetchAll(PDO::FETCH_COLUMN);
    }
    
    $response = ['success' => true, 'data' => $pages];
    echo "<pre>";
    echo json_encode($response, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
    echo "</pre>";
    
} catch (Exception $e) {
    echo "❌ Erreur : " . $e->getMessage() . "<br>";
    echo "Trace : <pre>" . $e->getTraceAsString() . "</pre>";
}
?>
