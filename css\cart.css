/* Styles spécifiques à la page du panier */
.cart-page {
    padding: 120px 0 60px;
}

.cart-page h2 {
    text-align: center;
    margin-bottom: 40px;
    color: #2c3e50;
}

.cart-container {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 30px;
}

/* Styles des éléments du panier */
.cart-items {
    background: #fff;
    border-radius: 10px;
    padding: 20px;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}

.cart-item {
    display: grid;
    grid-template-columns: auto 1fr auto;
    gap: 20px;
    padding: 20px 0;
    border-bottom: 1px solid #eee;
}

.cart-item:last-child {
    border-bottom: none;
}

.cart-item img {
    width: 100px;
    height: 150px;
    object-fit: cover;
    border-radius: 5px;
}

.item-details {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.item-details h3 {
    font-size: 1.2rem;
    color: #2c3e50;
    margin-bottom: 5px;
}

.item-price {
    font-weight: 700;
    color: #2c3e50;
}

.quantity-controls {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-top: 10px;
}

.quantity-controls button {
    background: #f8f9fa;
    border: 1px solid #ddd;
    border-radius: 5px;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.quantity-controls button:hover {
    background: #e9ecef;
}

.quantity-controls span {
    font-weight: 600;
    min-width: 30px;
    text-align: center;
}

.remove-item {
    color: #e74c3c;
    cursor: pointer;
    font-size: 1.2rem;
    transition: color 0.3s ease;
}

.remove-item:hover {
    color: #c0392b;
}

/* Styles du résumé du panier */
.cart-summary {
    background: #fff;
    border-radius: 10px;
    padding: 20px;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    height: fit-content;
}

.cart-summary h3 {
    color: #2c3e50;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 2px solid #eee;
}

.summary-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 15px;
    color: #666;
}

.summary-item.total {
    color: #2c3e50;
    font-weight: 700;
    font-size: 1.2rem;
    padding-top: 15px;
    border-top: 2px solid #eee;
    margin-top: 15px;
}

.checkout-button {
    width: 100%;
    padding: 15px;
    background: #2ecc71;
    color: #fff;
    border: none;
    border-radius: 5px;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: background-color 0.3s ease;
    font-family: 'Noto Sans Arabic', sans-serif;
}

.checkout-button:hover {
    background: #27ae60;
}

/* Styles du panier vide */
.empty-cart {
    text-align: center;
    padding: 60px 20px;
}

.empty-cart i {
    font-size: 4rem;
    color: #bdc3c7;
    margin-bottom: 20px;
}

.empty-cart h3 {
    color: #2c3e50;
    margin-bottom: 10px;
}

.empty-cart p {
    color: #7f8c8d;
    margin-bottom: 30px;
}

.continue-shopping {
    display: inline-block;
    padding: 12px 25px;
    background: #3498db;
    color: #fff;
    text-decoration: none;
    border-radius: 5px;
    transition: background-color 0.3s ease;
}

.continue-shopping:hover {
    background: #2980b9;
}

/* Responsive Design */
@media (max-width: 768px) {
    .cart-container {
        grid-template-columns: 1fr;
    }

    .cart-item {
        grid-template-columns: 1fr;
        text-align: center;
    }

    .cart-item img {
        margin: 0 auto;
    }

    .quantity-controls {
        justify-content: center;
    }

    .remove-item {
        text-align: center;
        margin-top: 10px;
    }
}