<?php
require_once __DIR__ . '/../php/config.php';

try {
    $pdo = getPDOConnection();
    
    // Lire le contenu du fichier de migration
    $migrationFile = __DIR__ . '/migrations/add_order_improvements.sql';
    $sql = file_get_contents($migrationFile);
    
    // Diviser le fichier en requêtes individuelles
    $queries = array_filter(array_map('trim', explode(';', $sql)));
    
    // Exécuter chaque requête
    foreach ($queries as $query) {
        if (!empty($query)) {
            $pdo->exec($query);
            echo "Requête exécutée avec succès: " . substr($query, 0, 50) . "...\n";
        }
    }
    
    echo "\nMigration terminée avec succès!\n";
    
} catch (PDOException $e) {
    echo "Erreur lors de la migration: " . $e->getMessage() . "\n";
    exit(1);
}