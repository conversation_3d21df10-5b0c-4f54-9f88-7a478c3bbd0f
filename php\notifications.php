<?php
require_once 'config.php';

class Notifications {
    private $pdo;

    public function __construct($pdo) {
        $this->pdo = $pdo;
    }

    // Créer une nouvelle notification
    public function create($type, $message, $referenceId = null) {
        try {
            $stmt = $this->pdo->prepare(
                'INSERT INTO notifications (type, message, reference_id) VALUES (?, ?, ?)'
            );
            $stmt->execute([$type, $message, $referenceId]);
            return ['success' => true, 'id' => $this->pdo->lastInsertId()];
        } catch(PDOException $e) {
            return ['error' => $e->getMessage()];
        }
    }

    // Récupérer les notifications non lues
    public function getUnread() {
        try {
            $stmt = $this->pdo->query(
                'SELECT * FROM notifications WHERE is_read = FALSE ORDER BY created_at DESC'
            );
            return $stmt->fetchAll();
        } catch(PDOException $e) {
            return ['error' => $e->getMessage()];
        }
    }

    // Marquer une notification comme lue
    public function markAsRead($id) {
        try {
            $stmt = $this->pdo->prepare('UPDATE notifications SET is_read = TRUE WHERE id = ?');
            $stmt->execute([$id]);
            return ['success' => true];
        } catch(PDOException $e) {
            return ['error' => $e->getMessage()];
        }
    }

    // Marquer toutes les notifications comme lues
    public function markAllAsRead() {
        try {
            $stmt = $this->pdo->query('UPDATE notifications SET is_read = TRUE WHERE is_read = FALSE');
            return ['success' => true, 'rows_affected' => $stmt->rowCount()];
        } catch(PDOException $e) {
            return ['error' => $e->getMessage()];
        }
    }

    // Supprimer les anciennes notifications (plus de 30 jours)
    public function cleanOldNotifications() {
        try {
            $stmt = $this->pdo->query(
                'DELETE FROM notifications WHERE created_at < DATE_SUB(NOW(), INTERVAL 30 DAY)'
            );
            return ['success' => true, 'rows_affected' => $stmt->rowCount()];
        } catch(PDOException $e) {
            return ['error' => $e->getMessage()];
        }
    }
}

// Instancier la classe Notifications
$pdo = getPDOConnection();
$notifications = new Notifications($pdo);

// Traitement des requêtes API
if ($_SERVER['REQUEST_METHOD'] === 'GET' && isAdminLoggedIn()) {
    if (isset($_GET['action'])) {
        switch ($_GET['action']) {
            case 'unread':
                echo json_encode($notifications->getUnread());
                break;
            case 'mark_read':
                if (isset($_GET['id'])) {
                    echo json_encode($notifications->markAsRead($_GET['id']));
                }
                break;
            case 'mark_all_read':
                echo json_encode($notifications->markAllAsRead());
                break;
        }
    }
}