<?php
// Afficher les erreurs pour le débogage
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Vérifier les extensions requises
$required_extensions = ['pdo', 'pdo_pgsql'];
foreach ($required_extensions as $ext) {
    if (!extension_loaded($ext)) {
        die("L'extension PHP {$ext} est requise mais n'est pas installée.");
    }
}

// Paramètres de connexion à la base de données
define('DB_HOST', 'localhost');
define('DB_PORT', '5432');
define('DB_NAME', 'poultraydz');
define('DB_USER', 'postgres');
define('DB_PASS', 'root');

// Options de connexion PDO
$options = [
    PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
    PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
    PDO::ATTR_EMULATE_PREPARES => false
];

// Création de la connexion PDO globale
try {
    $dsn = "pgsql:host=" . DB_HOST . ";port=" . DB_PORT . ";dbname=" . DB_NAME;
    $conn = new PDO($dsn, DB_USER, DB_PASS, $options);
    $conn->exec("SET timezone = 'Europe/Paris'");
    error_log("Connexion à la base de données PostgreSQL réussie");
} catch (PDOException $e) {
    error_log("Erreur de connexion à la base de données : " . $e->getMessage());
    error_log("Code d'erreur : " . $e->getCode());
    error_log("Trace : " . $e->getTraceAsString());
    throw new PDOException('Erreur de connexion à la base de données PostgreSQL');
}

// Fonction pour obtenir une connexion PDO (pour la compatibilité avec le code existant)
function getPDOConnection()
{
    global $conn;
    return $conn;
}

// Fonction pour sécuriser les entrées
function sanitize($data)
{
    $data = trim($data);
    $data = stripslashes($data);
    $data = htmlspecialchars($data);
    return $data;
}

// Fonction pour générer un identifiant unique pour les commandes
function generateOrderId()
{
    return uniqid('CMD_', true);
}

// Fonction pour formater le prix
function formatPrice($price)
{
    return number_format($price, 2, '.', ' ') . ' دج';
}

// Fonction pour vérifier si l'utilisateur est connecté en tant qu'admin
function isAdminLoggedIn()
{
    return isset($_SESSION['admin_id']);
}

// Démarrer la session si elle n'est pas déjà démarrée
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}
