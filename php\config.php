<?php
// Afficher les erreurs pour le débogage
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Vérifier les extensions requises
$required_extensions = ['pdo', 'pdo_mysql'];
foreach ($required_extensions as $ext) {
    if (!extension_loaded($ext)) {
        die("L'extension PHP {$ext} est requise mais n'est pas installée.");
    }
}

// Paramètres de connexion à la base de données
define('DB_HOST', 'localhost');
define('DB_PORT', '3307');
define('DB_NAME', 'mossab-landing-page');
define('DB_USER', 'root');
define('DB_PASS', '');

// Fonction pour obtenir une connexion PDO
function getPDOConnection() {
    try {
        // Vérification des paramètres de connexion
        error_log("Tentative de connexion à MySQL - Host: " . DB_HOST . ":" . DB_PORT);
        error_log("Nom de la base de données: " . DB_NAME);
        error_log("Utilisateur: " . DB_USER);
        
        // Création de la connexion PDO
        $dsn = "mysql:host=" . DB_HOST . ";port=" . DB_PORT . ";dbname=" . DB_NAME . ";charset=utf8mb4";
        error_log("DSN de connexion: " . $dsn);
        
        $options = [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            PDO::ATTR_EMULATE_PREPARES => false
        ];
        
        try {
            $pdo = new PDO($dsn, DB_USER, DB_PASS, $options);
            error_log("Connexion à la base de données réussie");
            
            // Test de la connexion
            $pdo->query("SELECT 1");
            error_log("Test de connexion réussi");
            
            return $pdo;
        } catch(PDOException $e) {
            throw new PDOException("Erreur lors de la création de la connexion PDO: " . $e->getMessage(), (int)$e->getCode());
        }
    } catch(PDOException $e) {
        error_log("Erreur de connexion à la base de données : " . $e->getMessage());
        error_log("Code d'erreur : " . $e->getCode());
        error_log("Trace : " . $e->getTraceAsString());
        throw $e;
    }
}

// Fonction pour sécuriser les entrées
function sanitize($data) {
    $data = trim($data);
    $data = stripslashes($data);
    $data = htmlspecialchars($data);
    return $data;
}

// Fonction pour générer un identifiant unique pour les commandes
function generateOrderId() {
    return uniqid('CMD_', true);
}

// Fonction pour formater le prix
function formatPrice($price) {
    return number_format($price, 2, '.', ' ') . ' دج';
}

// Fonction pour vérifier si l'utilisateur est connecté en tant qu'admin
function isAdminLoggedIn() {
    return isset($_SESSION['admin_id']);
}

// Démarrer la session si elle n'est pas déjà démarrée
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}