// Gestion du panier
let cart = JSON.parse(localStorage.getItem('cart')) || [];

// Mise à jour du compteur du panier
function updateCartCount() {
    const cartCount = document.querySelector('.cart-count');
    cartCount.textContent = cart.reduce((total, item) => total + item.quantity, 0);
}

// Ajouter au panier
function addToCart(bookId, title, price, image) {
    const existingItem = cart.find(item => item.id === bookId);
    
    if (existingItem) {
        existingItem.quantity += 1;
    } else {
        cart.push({
            id: bookId,
            title: title,
            price: price,
            image: image,
            quantity: 1
        });
    }
    
    localStorage.setItem('cart', JSON.stringify(cart));
    updateCartCount();
    
    // Animation de confirmation
    showNotification('تمت إضافة الكتاب إلى السلة');
}

// Afficher une notification
function showNotification(message) {
    const notification = document.createElement('div');
    notification.className = 'notification';
    notification.textContent = message;
    
    document.body.appendChild(notification);
    
    // Ajouter le style CSS pour la notification
    notification.style.cssText = `
        position: fixed;
        bottom: 20px;
        right: 20px;
        background-color: #2ecc71;
        color: white;
        padding: 15px 25px;
        border-radius: 5px;
        animation: slideIn 0.5s ease-out;
        z-index: 1000;
        font-family: 'Noto Sans Arabic', sans-serif;
    `;
    
    // Ajouter l'animation CSS
    const style = document.createElement('style');
    style.textContent = `
        @keyframes slideIn {
            from { transform: translateX(100%); opacity: 0; }
            to { transform: translateX(0); opacity: 1; }
        }
    `;
    document.head.appendChild(style);
    
    // Supprimer la notification après 3 secondes
    setTimeout(() => {
        notification.style.animation = 'slideOut 0.5s ease-in';
        setTimeout(() => notification.remove(), 500);
    }, 3000);
}

// Initialiser les événements
document.addEventListener('DOMContentLoaded', () => {
    updateCartCount();
    
    // Ajouter les gestionnaires d'événements pour les boutons
    const addToCartButtons = document.querySelectorAll('.add-to-cart');
    const buyNowButtons = document.querySelectorAll('.buy-now');
    
    addToCartButtons.forEach((button, index) => {
        button.addEventListener('click', () => {
            const bookCard = button.closest('.book-card');
            const title = bookCard.querySelector('h3').textContent;
            const price = parseFloat(bookCard.querySelector('.price').textContent);
            const image = bookCard.querySelector('img').src;
            
            addToCart(index + 1, title, price, image);
        });
    });
    
    buyNowButtons.forEach((button) => {
        button.addEventListener('click', () => {
            window.location.href = 'checkout.html';
        });
    });
});