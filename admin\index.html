<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة التحكم - متجر الكتب</title>
    <link rel="stylesheet" href="../css/style.css">
    <link rel="stylesheet" href="css/admin.css">
    <link rel="stylesheet" href="css/product-landing.css">

    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <div class="notifications-container" id="notificationsContainer"></div>
    <div class="admin-container">
        <!-- Sidebar -->
        <aside class="sidebar">
            <div class="logo">
                <h1>لوحة التحكم</h1>
            </div>
            <nav class="admin-nav">
                <ul>
                    <li class="active" data-section="dashboard">
                        <i class="fas fa-home"></i>
                        <span>الرئيسية</span>
                    </li>
                    <li data-section="books">
                        <i class="fas fa-book"></i>
                        <span>إدارة الكتب</span>
                    </li>
                    <li data-section="orders">
                        <i class="fas fa-shopping-cart"></i>
                        <span>الطلبات</span>
                    </li>
                    <li data-section="settings">
                        <i class="fas fa-cog"></i>
                        <span>الإعدادات</span>
                    </li>
                    <li id="logoutBtn">
                        <i class="fas fa-sign-out-alt"></i>
                        <span>تسجيل الخروج</span>
                    </li>
                </ul>
            </nav>
        </aside>

        <!-- Main Content -->
        <main class="main-content">
            <!-- Dashboard Section -->
            <section id="dashboard" class="content-section active">
                <h2>لوحة المعلومات</h2>
                <div class="stats-grid">
                    <div class="stat-card">
                        <i class="fas fa-book"></i>
                        <div class="stat-info">
                            <h3>إجمالي الكتب</h3>
                            <p id="totalBooks">0</p>
                        </div>
                    </div>
                    <div class="stat-card">
                        <i class="fas fa-shopping-cart"></i>
                        <div class="stat-info">
                            <h3>الطلبات الجديدة</h3>
                            <p id="newOrders">0</p>
                        </div>
                    </div>
                    <div class="stat-card">
                        <i class="fas fa-money-bill-wave"></i>
                        <div class="stat-info">
                            <h3>إجمالي المبيعات</h3>
                            <p id="totalSales">0 دج</p>
                        </div>
                    </div>
                </div>

                <div class="recent-orders">
                    <h3>آخر الطلبات</h3>
                    <div class="table-responsive">
                        <table id="recentOrdersTable">
                            <thead>
                                <tr>
                                    <th>رقم الطلب</th>
                                    <th>العميل</th>
                                    <th>المبلغ</th>
                                    <th>الحالة</th>
                                    <th>التاريخ</th>
                                </tr>
                            </thead>
                            <tbody></tbody>
                        </table>
                    </div>
                </div>
            </section>

            <!-- Books Management Section -->
            <section id="books" class="content-section">
                <h2>إدارة المنتجات</h2>
                <button id="addBookBtn" class="action-button">
                    <i class="fas fa-plus"></i> إضافة منتج جديد
                </button>

                <div class="table-responsive">
                    <table id="booksTable">
                        <thead>
                            <tr>
                                <th>الصورة</th>
                                <th>العنوان</th>
                                <th>المؤلف</th>
                                <th>السعر</th>
                                <th>المخزون</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody></tbody>
                    </table>
                </div>
            </section>

            <!-- Orders Management Section -->
            <section id="orders" class="content-section">
                <h2>إدارة الطلبات</h2>
                <div class="orders-filters">
                    <select id="orderStatusFilter">
                        <option value="all">جميع الطلبات</option>
                        <option value="en_attente">قيد الانتظار</option>
                        <option value="payé">تم الدفع</option>
                        <option value="expédié">تم الشحن</option>
                    </select>
                </div>

                <div class="table-responsive">
                    <table id="ordersTable">
                        <thead>
                            <tr>
                                <th>رقم الطلب</th>
                                <th>العميل</th>
                                <th>التفاصيل</th>
                                <th>المبلغ</th>
                                <th>الحالة</th>
                                <th>التاريخ</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody></tbody>
                    </table>
                </div>
            </section>

            <!-- Settings Section -->
            <section id="settings" class="content-section">
                <h2>الإعدادات</h2>
                <div class="settings-card">
                    <h3>تغيير كلمة المرور</h3>
                    <form id="changePasswordForm">
                        <div class="form-group">
                            <label>كلمة المرور الحالية</label>
                            <input type="password" id="currentPassword" required>
                        </div>
                        <div class="form-group">
                            <label>كلمة المرور الجديدة</label>
                            <input type="password" id="newPassword" required>
                        </div>
                        <div class="form-group">
                            <label>تأكيد كلمة المرور الجديدة</label>
                            <input type="password" id="confirmPassword" required>
                        </div>
                        <button type="submit" class="action-button">تحديث كلمة المرور</button>
                    </form>
                </div>

                <div class="settings-card">
                    <h3>إعدادات المتجر</h3>
                    <form id="storeSettingsForm">
                        <div class="form-group">
                            <label>اسم المتجر</label>
                            <input type="text" id="storeName" required>
                        </div>
                        <div class="form-group">
                            <label>رقم الهاتف</label>
                            <input type="tel" id="storePhone" required>
                        </div>
                        <div class="form-group">
                            <label>البريد الإلكتروني</label>
                            <input type="email" id="storeEmail" required>
                        </div>
                        <div class="form-group">
                            <label>العنوان</label>
                    <textarea id="storeAddress" class="tinymce" required></textarea>
                        </div>
                        <button type="submit" class="action-button">حفظ الإعدادات</button>
                    </form>
                </div>
            </section>
        </main>
    </div>

    <!-- Add/Edit Book Modal -->
    <div id="bookModal" class="modal">
    
        <div class="modal-content">
            <span class="close">&times;</span>
            <h3 id="modalTitle">إضافة منتج جديد</h3>
            <form id="bookForm">
                <div class="form-group">
                    <label>نوع المنتج</label>
                    <select id="productType" class="product-type-select" required>
                        <option value="book">كتاب</option>
                        <option value="backpack">حقيبة ظهر</option>
                        <option value="laptop">حاسوب محمول</option>
                    </select>
                </div>

                <!-- Common Fields -->
                <div class="form-group">
                    <label>عنوان المنتج</label>
                    <input type="text" id="productTitle" required>
                </div>
                <div class="form-group">
                    <label>الوصف</label>
                    <textarea id="productDescription" class="tinymce" required></textarea>
                </div>
                <div class="form-group">
                    <label>السعر (دج)</label>
                    <input type="number" id="productPrice" min="0" step="0.01" required>
                </div>
                <div class="form-group">
                    <label>المخزون</label>
                    <input type="number" id="productStock" min="0" required>
                </div>

                <!-- Book Specific Fields -->
                <div id="bookFields" class="field-group active">
                    <div class="form-group">
                        <label>المؤلف</label>
                        <input type="text" id="bookAuthor">
                    </div>
                </div>

                <!-- Backpack Specific Fields -->
                <div id="backpackFields" class="field-group">
                    <div class="form-group">
                        <label>المواد</label>
                        <input type="text" id="backpackMaterial">
                    </div>
                    <div class="form-group">
                        <label>السعة (لتر)</label>
                        <input type="number" id="backpackCapacity" min="0">
                    </div>
                </div>

                <!-- Laptop Specific Fields -->
                <div id="laptopFields" class="field-group">
                    <div class="form-group">
                        <label>المعالج</label>
                        <input type="text" id="laptopProcessor">
                    </div>
                    <div class="form-group">
                        <label>الذاكرة العشوائية (GB)</label>
                        <input type="number" id="laptopRam" min="0">
                    </div>
                    <div class="form-group">
                        <label>التخزين (GB)</label>
                        <input type="number" id="laptopStorage" min="0">
                    </div>
                </div>

                <div class="form-group">
                    <label>صورة المنتج</label>
                    <input type="file" id="productImage" accept="image/*">
                </div>
                <button type="submit" class="action-button">حفظ</button>
            </form>
        </div>
    </div>

    <!-- Order Details Modal -->
    <div id="orderModal" class="modal">
        <div class="modal-content">
            <span class="close">&times;</span>
            <h3>تفاصيل الطلب</h3>
            <div id="orderDetails"></div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/tinymce/6.7.2/tinymce.min.js" crossorigin="anonymous" referrerpolicy="no-referrer"></script>
    <script src="js/tinymce-config.js"></script>
    <script src="js/admin.js"></script>
    <script src="js/product-landing.js"></script>
</body>
</html>
