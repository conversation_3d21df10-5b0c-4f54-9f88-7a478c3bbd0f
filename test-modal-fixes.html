<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔧 Test Corrections Modal</title>
    <style>
        body {
            font-family: 'Arial', sans-serif;
            padding: 20px;
            background: #f8fafc;
            color: #1e293b;
            line-height: 1.6;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 16px;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }
        .fix-section {
            margin: 30px 0;
            padding: 25px;
            border: 1px solid #e2e8f0;
            border-radius: 12px;
            background: #f9fafb;
        }
        .fix-section h3 {
            color: #374151;
            margin-bottom: 20px;
            font-size: 1.25rem;
        }
        .status {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.875rem;
            font-weight: 500;
        }
        .status.fixed {
            background: #dcfce7;
            color: #166534;
        }
        .test-item {
            margin: 15px 0;
            padding: 15px;
            background: white;
            border-radius: 8px;
            border-left: 4px solid #10b981;
        }
        .test-title {
            font-weight: 600;
            margin-bottom: 8px;
        }
        .test-description {
            font-size: 0.9rem;
            color: #6b7280;
        }
        button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            margin: 10px 5px;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
        }
        .console-output {
            background: #1f2937;
            color: #f9fafb;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 0.875rem;
            max-height: 400px;
            overflow-y: auto;
            margin-top: 15px;
        }
        .iframe-container {
            margin-top: 20px;
            border: 1px solid #e2e8f0;
            border-radius: 12px;
            overflow: hidden;
        }
        iframe {
            width: 100%;
            height: 600px;
            border: none;
        }
        .success-alert {
            background: #f0fdf4;
            border: 1px solid #bbf7d0;
            color: #166534;
            padding: 20px;
            border-radius: 12px;
            margin-bottom: 30px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Test des Corrections du Modal Landing Page</h1>
        
        <div class="success-alert">
            <h3>✅ Corrections Appliquées</h3>
            <p>Les corrections suivantes ont été appliquées pour résoudre les problèmes du modal :</p>
        </div>

        <div class="fix-section">
            <h3>🔧 Correction 1: Ouverture Instantanée du Modal <span class="status fixed">CORRIGÉ</span></h3>
            <div class="test-item">
                <div class="test-title">✅ Visibilité de la Section</div>
                <div class="test-description">
                    <strong>Problème :</strong> Le modal ne s'ouvrait pas instantanément car la section n'était pas visible.<br>
                    <strong>Solution :</strong> Vérification et activation de la visibilité de la section avant l'ouverture du modal
                </div>
            </div>
        </div>

        <div class="fix-section">
            <h3>🔧 Correction 2: Configuration Base de Données <span class="status fixed">CORRIGÉ</span></h3>
            <div class="test-item">
                <div class="test-title">✅ PostgreSQL au lieu de MySQL</div>
                <div class="test-description">
                    <strong>Problème :</strong> Configuration MySQL dans config.php mais utilisation de PostgreSQL.<br>
                    <strong>Solution :</strong> Changement vers PostgreSQL avec les bons paramètres (port 5432, base poultraydz)
                </div>
            </div>
        </div>

        <div class="fix-section">
            <h3>🔧 Correction 3: Gestion d'Erreurs API <span class="status fixed">CORRIGÉ</span></h3>
            <div class="test-item">
                <div class="test-title">✅ Meilleure Gestion des Erreurs</div>
                <div class="test-description">
                    <strong>Problème :</strong> Erreurs 500 non gérées et réponses HTML au lieu de JSON.<br>
                    <strong>Solution :</strong> Ajout de logs détaillés et gestion des erreurs de parsing JSON
                </div>
            </div>
        </div>

        <div class="fix-section">
            <h3>🔧 Correction 4: Boutons de Fermeture <span class="status fixed">CORRIGÉ</span></h3>
            <div class="test-item">
                <div class="test-title">✅ Bouton X et Bouton Annuler</div>
                <div class="test-description">
                    <strong>Problème :</strong> Difficultés pour fermer le modal.<br>
                    <strong>Solution :</strong> Ajout d'un bouton X en haut et amélioration du bouton Annuler
                </div>
            </div>
        </div>

        <div class="fix-section">
            <h3>🧪 Tests de Vérification</h3>
            <button onclick="testModalOpening()">Test Ouverture Modal</button>
            <button onclick="testDatabaseConnection()">Test Connexion DB</button>
            <button onclick="testFormSubmission()">Test Soumission Formulaire</button>
            <button onclick="openAdminPanel()">Ouvrir Panel Admin</button>
            
            <div class="console-output" id="console-output"></div>
        </div>

        <div class="fix-section">
            <h3>📋 Instructions de Test</h3>
            <div class="test-item">
                <div class="test-title">1. Test d'Ouverture du Modal</div>
                <div class="test-description">
                    • Aller dans Admin Panel → صفحات هبوط<br>
                    • Cliquer sur "أَضف صفحة هبوط"<br>
                    • Le modal devrait s'ouvrir instantanément<br>
                    • Vérifier que les produits se chargent dans le dropdown
                </div>
            </div>
            <div class="test-item">
                <div class="test-title">2. Test de Fermeture du Modal</div>
                <div class="test-description">
                    • Cliquer sur le bouton X en haut à droite<br>
                    • Ou cliquer sur le bouton "إلغاء"<br>
                    • Le modal devrait se fermer correctement
                </div>
            </div>
            <div class="test-item">
                <div class="test-title">3. Test de Sauvegarde</div>
                <div class="test-description">
                    • Remplir le formulaire avec un produit, titre et contenu<br>
                    • Cliquer sur "حفظ"<br>
                    • Vérifier dans la console s'il y a des erreurs<br>
                    • La page devrait se sauvegarder sans erreur 500
                </div>
            </div>
        </div>

        <div class="iframe-container" style="display: none;" id="iframe-container">
            <iframe id="admin-frame"></iframe>
        </div>
    </div>

    <script>
        const consoleOutput = document.getElementById('console-output');

        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.innerHTML = `[${timestamp}] ${type.toUpperCase()}: ${message}`;
            consoleOutput.appendChild(logEntry);
            consoleOutput.scrollTop = consoleOutput.scrollHeight;
        }

        function testModalOpening() {
            log('🧪 Testing modal opening...');
            
            const iframe = document.getElementById('admin-frame');
            if (!iframe || !iframe.contentWindow) {
                log('⚠️ Admin panel not loaded yet');
                return;
            }
            
            try {
                const landingPagesManager = iframe.contentWindow.landingPagesManager;
                if (landingPagesManager) {
                    log('✅ Landing Pages Manager found');
                    log('Testing modal opening...');
                    landingPagesManager.openModal();
                    log('✅ Modal opening command sent');
                } else {
                    log('❌ Landing Pages Manager not found');
                }
            } catch (error) {
                log('❌ Error testing modal: ' + error.message);
            }
        }

        function testDatabaseConnection() {
            log('🧪 Testing database connection...');
            
            fetch('/php/api/products.php')
                .then(response => {
                    log(`Response status: ${response.status}`);
                    return response.text();
                })
                .then(text => {
                    log('Raw response: ' + text.substring(0, 200));
                    try {
                        const data = JSON.parse(text);
                        log('✅ Valid JSON response received');
                        if (data.success && data.products) {
                            log(`✅ Found ${data.products.length} products`);
                        }
                    } catch (e) {
                        log('❌ Invalid JSON response');
                    }
                })
                .catch(error => {
                    log('❌ Database connection error: ' + error.message);
                });
        }

        function testFormSubmission() {
            log('🧪 Testing form submission...');
            log('This test requires manual form filling in the admin panel');
            log('Fill the form and check console for detailed error logs');
        }

        function openAdminPanel() {
            log('🚀 Opening admin panel...');
            const iframe = document.getElementById('admin-frame');
            const container = document.getElementById('iframe-container');
            
            container.style.display = 'block';
            iframe.src = '/admin/index.html?' + Date.now();
            
            iframe.onload = function() {
                log('✅ Admin panel loaded');
                
                setTimeout(() => {
                    testDatabaseConnection();
                }, 2000);
            };
        }

        document.addEventListener('DOMContentLoaded', () => {
            log('🔧 Modal fixes test page loaded');
            log('Click "Ouvrir Panel Admin" to start testing');
        });
    </script>
</body>
</html>
