<?php
/**
 * Security utilities for production deployment
 */

// Prevent direct access
if (!defined('SECURITY_CHECK')) {
    die('Direct access not allowed');
}

/**
 * Sanitize input data
 */
function sanitizeInput($data) {
    if (is_array($data)) {
        return array_map('sanitizeInput', $data);
    }
    
    $data = trim($data);
    $data = stripslashes($data);
    $data = htmlspecialchars($data, ENT_QUOTES, 'UTF-8');
    return $data;
}

/**
 * Validate CSRF token
 */
function validateCSRFToken($token) {
    if (!isset($_SESSION['csrf_token'])) {
        return false;
    }
    
    return hash_equals($_SESSION['csrf_token'], $token);
}

/**
 * Generate CSRF token
 */
function generateCSRFToken() {
    if (!isset($_SESSION['csrf_token'])) {
        $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
    }
    return $_SESSION['csrf_token'];
}

/**
 * Rate limiting
 */
function checkRateLimit($action, $limit = 10, $window = 60) {
    $key = $action . '_' . $_SERVER['REMOTE_ADDR'];
    
    if (!isset($_SESSION['rate_limit'])) {
        $_SESSION['rate_limit'] = [];
    }
    
    $now = time();
    
    // Clean old entries
    $_SESSION['rate_limit'] = array_filter($_SESSION['rate_limit'], function($timestamp) use ($now, $window) {
        return ($now - $timestamp) < $window;
    });
    
    // Count requests for this action
    $count = isset($_SESSION['rate_limit'][$key]) ? count($_SESSION['rate_limit'][$key]) : 0;
    
    if ($count >= $limit) {
        return false;
    }
    
    // Add current request
    if (!isset($_SESSION['rate_limit'][$key])) {
        $_SESSION['rate_limit'][$key] = [];
    }
    $_SESSION['rate_limit'][$key][] = $now;
    
    return true;
}

/**
 * Validate file upload
 */
function validateFileUpload($file, $allowedTypes = ['image/jpeg', 'image/png', 'image/gif'], $maxSize = 5242880) {
    // Check if file was uploaded
    if (!isset($file['tmp_name']) || !is_uploaded_file($file['tmp_name'])) {
        return ['valid' => false, 'error' => 'ملف غير صحيح'];
    }
    
    // Check file size
    if ($file['size'] > $maxSize) {
        return ['valid' => false, 'error' => 'حجم الملف كبير جداً'];
    }
    
    // Check file type
    $finfo = finfo_open(FILEINFO_MIME_TYPE);
    $mimeType = finfo_file($finfo, $file['tmp_name']);
    finfo_close($finfo);
    
    if (!in_array($mimeType, $allowedTypes)) {
        return ['valid' => false, 'error' => 'نوع الملف غير مسموح'];
    }
    
    // Check for malicious content
    $content = file_get_contents($file['tmp_name']);
    if (strpos($content, '<?php') !== false || strpos($content, '<script') !== false) {
        return ['valid' => false, 'error' => 'محتوى الملف غير آمن'];
    }
    
    return ['valid' => true];
}

/**
 * Log security events
 */
function logSecurityEvent($event, $details = '') {
    $logFile = '../logs/security.log';
    $logDir = dirname($logFile);
    
    if (!is_dir($logDir)) {
        mkdir($logDir, 0755, true);
    }
    
    $timestamp = date('Y-m-d H:i:s');
    $ip = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
    $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? 'unknown';
    
    $logEntry = "[{$timestamp}] IP: {$ip} | Event: {$event} | Details: {$details} | User-Agent: {$userAgent}" . PHP_EOL;
    
    file_put_contents($logFile, $logEntry, FILE_APPEND | LOCK_EX);
}

/**
 * Check if admin is logged in
 */
function requireAdminLogin() {
    if (!isset($_SESSION['admin_id'])) {
        logSecurityEvent('UNAUTHORIZED_ACCESS', 'Attempt to access admin area without login');
        http_response_code(401);
        echo json_encode(['success' => false, 'message' => 'غير مصرح بالوصول']);
        exit;
    }
}

/**
 * Validate admin session
 */
function validateAdminSession() {
    if (!isset($_SESSION['admin_id']) || !isset($_SESSION['admin_login_time'])) {
        return false;
    }
    
    // Check session timeout (24 hours)
    if (time() - $_SESSION['admin_login_time'] > 86400) {
        session_destroy();
        return false;
    }
    
    return true;
}

/**
 * Generate secure filename
 */
function generateSecureFilename($originalName) {
    $extension = pathinfo($originalName, PATHINFO_EXTENSION);
    $filename = bin2hex(random_bytes(16)) . '.' . $extension;
    return $filename;
}

/**
 * Validate URL
 */
function validateURL($url) {
    return filter_var($url, FILTER_VALIDATE_URL) !== false;
}

/**
 * Clean output for JSON response
 */
function cleanJsonOutput($data) {
    return json_encode($data, JSON_UNESCAPED_UNICODE | JSON_HEX_TAG | JSON_HEX_APOS | JSON_HEX_QUOT | JSON_HEX_AMP);
}
?>
