<?php
// Test de connexion à la base de données MySQL
header('Content-Type: application/json');

// Afficher les erreurs pour le débogage
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

echo "🔧 Test de Connexion MySQL\n";
echo "========================\n\n";

// Paramètres de connexion
$host = 'localhost';
$port = '3307';
$dbname = 'mossab-landing-page';
$username = 'root';
$password = '';

echo "📋 Paramètres de connexion :\n";
echo "Host: $host\n";
echo "Port: $port\n";
echo "Database: $dbname\n";
echo "User: $username\n";
echo "Password: " . (empty($password) ? '(vide)' : '***') . "\n\n";

// Test des extensions PHP
echo "🔍 Vérification des extensions PHP :\n";
$required_extensions = ['pdo', 'pdo_mysql'];
foreach ($required_extensions as $ext) {
    if (extension_loaded($ext)) {
        echo "✅ $ext : Installé\n";
    } else {
        echo "❌ $ext : NON installé\n";
    }
}
echo "\n";

// Test de connexion
echo "🔌 Test de connexion à MySQL :\n";
try {
    $dsn = "mysql:host=$host;port=$port;dbname=$dbname;charset=utf8mb4";
    echo "DSN: $dsn\n";
    
    $options = [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
        PDO::ATTR_EMULATE_PREPARES => false
    ];
    
    $conn = new PDO($dsn, $username, $password, $options);
    $conn->exec("SET time_zone = '+01:00'");
    
    echo "✅ Connexion réussie !\n\n";
    
    // Test de requête simple
    echo "📊 Test de requête :\n";
    $stmt = $conn->query("SELECT DATABASE() as current_db, VERSION() as mysql_version");
    $result = $stmt->fetch();
    
    echo "Base de données actuelle: " . $result['current_db'] . "\n";
    echo "Version MySQL: " . $result['mysql_version'] . "\n\n";
    
    // Test des tables
    echo "📋 Tables disponibles :\n";
    $stmt = $conn->query("SHOW TABLES");
    $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    if (empty($tables)) {
        echo "⚠️ Aucune table trouvée dans la base de données\n";
    } else {
        foreach ($tables as $table) {
            echo "- $table\n";
        }
    }
    echo "\n";
    
    // Test spécifique pour la table produits
    echo "🛍️ Test de la table produits :\n";
    try {
        $stmt = $conn->query("SELECT COUNT(*) as count FROM produits");
        $count = $stmt->fetch()['count'];
        echo "✅ Table produits trouvée avec $count enregistrements\n";
        
        // Afficher quelques produits
        if ($count > 0) {
            $stmt = $conn->query("SELECT id, titre, actif FROM produits LIMIT 3");
            $products = $stmt->fetchAll();
            echo "Exemples de produits :\n";
            foreach ($products as $product) {
                $status = $product['actif'] ? 'Actif' : 'Inactif';
                echo "  - ID: {$product['id']}, Titre: {$product['titre']}, Status: $status\n";
            }
        }
    } catch (PDOException $e) {
        echo "❌ Erreur avec la table produits: " . $e->getMessage() . "\n";
    }
    
} catch (PDOException $e) {
    echo "❌ Erreur de connexion: " . $e->getMessage() . "\n";
    echo "Code d'erreur: " . $e->getCode() . "\n";
    
    // Suggestions de résolution
    echo "\n🔧 Suggestions de résolution :\n";
    if (strpos($e->getMessage(), 'Connection refused') !== false) {
        echo "- Vérifiez que MySQL est démarré\n";
        echo "- Vérifiez que le port 3307 est correct\n";
    }
    if (strpos($e->getMessage(), 'Access denied') !== false) {
        echo "- Vérifiez le nom d'utilisateur et mot de passe\n";
        echo "- Vérifiez les permissions de l'utilisateur\n";
    }
    if (strpos($e->getMessage(), 'Unknown database') !== false) {
        echo "- Vérifiez que la base de données 'mossab-landing-page' existe\n";
        echo "- Créez la base de données si nécessaire\n";
    }
}

echo "\n🏁 Test terminé\n";
?>
