// TinyMCE Configuration
function initTinyMCE() {
    // Remove any existing TinyMCE instances first
    if (typeof tinymce !== 'undefined') {
        tinymce.remove();
    }

    tinymce.init({
        selector: 'textarea.tinymce',
        license_key: 'gpl', // Use GPL license to avoid API key requirement
        language: 'ar',
        directionality: 'rtl',
        content_css: [
            'https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@400;500;600;700&display=swap',
            'css/tinymce-content.css'
        ],
        font_family_formats: 'Noto Sans Arabic=Noto Sans Arabic,sans-serif',
        plugins: [
            'advlist', 'autolink', 'lists', 'link', 'image', 'charmap',
            'preview', 'anchor', 'searchreplace', 'visualblocks', 'code',
            'fullscreen', 'insertdatetime', 'media', 'table', 'help', 'wordcount'
        ],
        toolbar: 'undo redo | formatselect | bold italic | alignleft aligncenter alignright alignjustify | bullist numlist outdent indent | link image | preview fullscreen',
        promotion: false,
        branding: false,
        min_height: 400,
        resize: true,
        paste_data_images: true,
        convert_urls: false,
        relative_urls: false,
        remove_script_host: false,
        readonly: false, // Explicitly set to false to ensure editors are editable
        menubar: false,
        statusbar: true,
        content_style: 'body { font-family: "Noto Sans Arabic", sans-serif; direction: rtl; text-align: right; }',
        setup: function(editor) {
            editor.on('init', function() {
                editor.getContainer().style.direction = 'rtl';
                // Ensure the editor is not read-only
                editor.mode.set('design');
            });

            // Add error handling
            editor.on('LoadContent', function() {
                console.log('TinyMCE content loaded for:', editor.id);
            });
        },
        init_instance_callback: function(editor) {
            console.log('TinyMCE initialized for:', editor.id);
            // Ensure editor is editable
            editor.setMode('design');
        }
    }).catch(function(error) {
        console.error('TinyMCE initialization failed:', error);
        // Fallback to regular textareas if TinyMCE fails
        document.querySelectorAll('textarea.tinymce').forEach(textarea => {
            textarea.style.display = 'block';
            textarea.style.minHeight = '400px';
        });
    });
}
