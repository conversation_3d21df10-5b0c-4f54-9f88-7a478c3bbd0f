// TinyMCE Configuration - Self-hosted version without API key
function initTinyMCE() {
    // Remove any existing TinyMCE instances first
    if (typeof tinymce !== 'undefined') {
        tinymce.remove();
    }

    tinymce.init({
        selector: 'textarea.tinymce',
        // No license_key needed for self-hosted version
        language: 'ar',
        directionality: 'rtl',
        content_css: [
            'https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@400;500;600;700&display=swap'
        ],
        font_family_formats: 'Noto Sans Arabic=Noto Sans Arabic,sans-serif',
        plugins: [
            'advlist', 'autolink', 'lists', 'link', 'image', 'charmap',
            'preview', 'anchor', 'searchreplace', 'visualblocks', 'code',
            'fullscreen', 'insertdatetime', 'media', 'table', 'wordcount'
        ],
        toolbar: 'undo redo | formatselect | bold italic | alignright aligncenter alignleft | bullist numlist outdent indent | link image | preview fullscreen',
        promotion: false,
        branding: false,
        min_height: 400,
        resize: true,
        paste_data_images: true,
        convert_urls: false,
        relative_urls: false,
        remove_script_host: false,
        readonly: false, // Explicitly set to false
        menubar: false,
        statusbar: true,
        content_style: 'body { font-family: "Noto Sans Arabic", sans-serif; direction: rtl; text-align: right; }',
        setup: function(editor) {
            editor.on('init', function() {
                console.log('TinyMCE initialized for:', editor.id);
                editor.getContainer().style.direction = 'rtl';
                // Force editable mode
                editor.mode.set('design');
                editor.setContent(editor.getContent()); // Refresh content
            });
        },
        init_instance_callback: function(editor) {
            console.log('TinyMCE ready for:', editor.id);
            // Ensure editor is fully editable
            editor.setMode('design');
            // Remove any readonly attributes
            editor.getBody().removeAttribute('readonly');
            editor.getBody().contentEditable = true;
        }
    }).catch(function(error) {
        console.error('TinyMCE initialization failed:', error);
        // Fallback to regular textareas if TinyMCE fails
        document.querySelectorAll('textarea.tinymce').forEach(textarea => {
            textarea.style.display = 'block';
            textarea.style.minHeight = '400px';
            textarea.style.border = '1px solid #ccc';
            textarea.style.padding = '10px';
        });
    });
}
