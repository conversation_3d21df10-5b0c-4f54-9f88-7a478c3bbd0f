// TinyMCE Configuration
function initTinyMCE() {
    tinymce.init({
        selector: 'textarea.tinymce',
        language: 'ar',
        language_url: '../admin/js/langs/ar.js', // Local Arabic language file
        directionality: 'rtl',
        content_css: [
            'https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@400;500;600;700&display=swap',
            '../admin/css/tinymce-content.css'
        ],
        font_family_formats: 'Noto Sans Arabic=Noto Sans Arabic,sans-serif',
        plugins: [
            'advlist', 'autolink', 'lists', 'link', 'image', 'charmap',
            'preview', 'anchor', 'searchreplace', 'visualblocks', 'code',
            'fullscreen', 'insertdatetime', 'media', 'table', 'help', 'wordcount'
        ],
        toolbar: 'undo redo | formatselect | bold italic | alignleft aligncenter alignright alignjustify | bullist numlist outdent indent | link image | preview fullscreen',
        promotion: false,
        branding: false,
        min_height: 400,
        resize: true,
        paste_data_images: true,
        convert_urls: false,
        relative_urls: false,
        remove_script_host: false,
        setup: function(editor) {
            editor.on('init', function() {
                editor.getContainer().style.direction = 'rtl';
            });
        }
    });
}