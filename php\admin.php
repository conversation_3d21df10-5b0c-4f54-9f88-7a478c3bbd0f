<?php
header('Content-Type: application/json; charset=utf-8');
require_once 'config.php';

class Admin {
    private $pdo;

    public function __construct() {
        $this->pdo = getPDOConnection();
        if (!$this->pdo) {
            throw new Exception('Impossible de se connecter à la base de données');
        }
    }

    // Authentification de l'administrateur
    public function login($username, $password) {
        try {
            $stmt = $this->pdo->prepare('SELECT * FROM admins WHERE nom_utilisateur = ?');
            $stmt->execute([sanitize($username)]);
            $admin = $stmt->fetch();

            if ($admin && password_verify($password, $admin['mot_de_passe'])) {
                $_SESSION['admin_id'] = $admin['id'];
                $_SESSION['admin_username'] = $admin['nom_utilisateur'];
                return ['success' => true];
            }

            return ['error' => 'Nom d\'utilisateur ou mot de passe incorrect'];
        } catch(PDOException $e) {
            return ['error' => $e->getMessage()];
        }
    }

    // Déconnexion
    public function logout() {
        session_destroy();
        return ['success' => true];
    }

    // Créer un nouvel administrateur
    public function createAdmin($username, $password) {
        try {
            // Vérifier si l'administrateur existe déjà
            $stmt = $this->pdo->prepare('SELECT id FROM admins WHERE nom_utilisateur = ?');
            $stmt->execute([sanitize($username)]);
            if ($stmt->fetch()) {
                return ['error' => 'Ce nom d\'utilisateur existe déjà'];
            }

            // Créer le nouvel administrateur
            $stmt = $this->pdo->prepare(
                'INSERT INTO admins (nom_utilisateur, mot_de_passe) VALUES (?, ?)'
            );
            $stmt->execute([
                sanitize($username),
                password_hash($password, PASSWORD_DEFAULT)
            ]);

            return ['success' => true, 'id' => $this->pdo->lastInsertId()];
        } catch(PDOException $e) {
            return ['error' => $e->getMessage()];
        }
    }

    // Modifier le mot de passe
    public function changePassword($adminId, $currentPassword, $newPassword) {
        try {
            // Vérifier l'ancien mot de passe
            $stmt = $this->pdo->prepare('SELECT mot_de_passe FROM admins WHERE id = ?');
            $stmt->execute([$adminId]);
            $admin = $stmt->fetch();

            if (!$admin || !password_verify($currentPassword, $admin['mot_de_passe'])) {
                return ['error' => 'Mot de passe actuel incorrect'];
            }

            // Mettre à jour le mot de passe
            $stmt = $this->pdo->prepare(
                'UPDATE admins SET mot_de_passe = ? WHERE id = ?'
            );
            $stmt->execute([
                password_hash($newPassword, PASSWORD_DEFAULT),
                $adminId
            ]);

            return ['success' => true];
        } catch(PDOException $e) {
            return ['error' => $e->getMessage()];
        }
    }
}

// Instancier la classe Admin
try {
    $admin = new Admin();
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['error' => $e->getMessage()]);
    exit;
}

// Traitement des requêtes API
// Traitement des requêtes API
try {
    $rawInput = file_get_contents('php://input');
    error_log('Données brutes reçues : ' . $rawInput);
    $data = json_decode($rawInput, true);
    error_log('Données décodées : ' . print_r($data, true));

    if (isset($_GET['action'])) {
        switch ($_GET['action']) {
            case 'login':
                if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($data['username']) && isset($data['password'])) {
                    $result = $admin->login($data['username'], $data['password']);
                    if (isset($result['error'])) {
                        http_response_code(401);
                    }
                    echo json_encode($result);
                }
                break;

            case 'logout':
                if ($_SERVER['REQUEST_METHOD'] === 'POST') {
                    echo json_encode($admin->logout());
                }
                break;

            case 'create':
                if ($_SERVER['REQUEST_METHOD'] === 'POST') {
                    if (isAdminLoggedIn() && isset($data['username']) && isset($data['password'])) {
                        $result = $admin->createAdmin($data['username'], $data['password']);
                        if (isset($result['error'])) {
                            http_response_code(400);
                        }
                        echo json_encode($result);
                    } else {
                        http_response_code(403);
                        echo json_encode(['error' => 'Accès non autorisé']);
                    }
                }
                break;

            case 'change-password':
                if ($_SERVER['REQUEST_METHOD'] === 'POST') {
                    if (isAdminLoggedIn() && isset($data['current_password']) && isset($data['new_password'])) {
                        $result = $admin->changePassword(
                            $_SESSION['admin_id'],
                            $data['current_password'],
                            $data['new_password']
                        );
                        if (isset($result['error'])) {
                            http_response_code(400);
                        }
                        echo json_encode($result);
                    } else {
                        http_response_code(403);
                        echo json_encode(['error' => 'Accès non autorisé']);
                    }
                }
                break;

            case 'check':
                if ($_SERVER['REQUEST_METHOD'] === 'GET') {
                    echo json_encode(['logged_in' => isAdminLoggedIn()]);
                }
                break;

            default:
                http_response_code(404);
                echo json_encode(['error' => 'Action non trouvée']);
        }
    } else {
        http_response_code(400);
        echo json_encode(['error' => 'Action non spécifiée']);
    }
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['error' => 'Erreur serveur: ' . $e->getMessage()]);
}