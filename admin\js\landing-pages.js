// Landing Pages Manager - Clean Implementation
// Use the existing notificationManager from admin.js

// Utility function for safe API calls with proper error handling
async function safeApiCall(url, options = {}) {
    try {
        const response = await fetch(url, options);
        
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        
        const text = await response.text();
        
        // Check if response is empty
        if (!text.trim()) {
            console.warn('Empty response from API:', url);
            return [];
        }
        
        // Try to parse JSON
        try {
            return JSON.parse(text);
        } catch (parseError) {
            console.error('JSON parse error for response:', text);
            throw new Error('Invalid JSON response from server');
        }
    } catch (error) {
        console.error('API call failed:', error);
        throw error;
    }
}

// Landing Pages Manager - Single Instance
const landingPagesManager = {
    initialized: false,
    modal: null,
    form: null,
    productSelect: null,
    imagePreview: null,
    addButton: null,

    init() {
        // Prevent multiple initializations
        if (this.initialized) {
            console.log('Landing Pages Manager already initialized');
            return;
        }
        
        console.log('Initializing Landing Pages Manager');
        
        // Check if elements exist before proceeding
        this.modal = document.getElementById('landingPageModal');
        this.form = document.getElementById('landingPageForm');
        this.productSelect = document.getElementById('productSelect');
        this.imagePreview = document.getElementById('imagePreview');
        this.addButton = document.getElementById('addLandingPageBtn');

        if (!this.modal || !this.form || !this.productSelect || !this.addButton) {
            console.error('Required elements not found for landing pages manager');
            console.log('Elements found:', {
                modal: !!this.modal,
                form: !!this.form,
                productSelect: !!this.productSelect,
                addButton: !!this.addButton
            });
            return;
        }

        this.bindEvents();
        this.loadProducts();
        this.loadLandingPages();
        
        // Mark as initialized
        this.initialized = true;
        console.log('Landing Pages Manager initialized successfully');
    },

    bindEvents() {
        console.log('Binding events');
        
        // Add button click handler
        if (this.addButton) {
            this.addButton.addEventListener('click', (e) => {
                e.preventDefault();
                console.log('Add landing page button clicked');
                this.openModal();
            });
        }
        
        // Form submit handler
        if (this.form) {
            this.form.addEventListener('submit', (e) => this.handleSubmit(e));
        }
        
        // Image upload handler
        const imageInput = document.getElementById('landingPageImages');
        if (imageInput) {
            imageInput.addEventListener('change', (e) => this.handleImageUpload(e));
        }

        // Close button handler
        const closeButton = this.modal ? this.modal.querySelector('.cancel-button') : null;
        if (closeButton) {
            closeButton.addEventListener('click', (e) => {
                e.preventDefault();
                this.closeModal();
            });
        }

        // Close modal when clicking outside
        if (this.modal) {
            window.addEventListener('click', (event) => {
                if (event.target === this.modal) {
                    this.closeModal();
                }
            });
        }
    },

    async loadProducts() {
        try {
            const products = await safeApiCall('../php/api/products.php');

            this.productSelect.innerHTML = '<option value="">اختر منتجاً</option>';
            
            if (Array.isArray(products)) {
                products.forEach(product => {
                    const option = document.createElement('option');
                    option.value = product.id;
                    option.textContent = product.titre || product.title || `Product ${product.id}`;
                    this.productSelect.appendChild(option);
                });
            }
        } catch (error) {
            console.error('Error loading products:', error);
            notificationManager.showError('حدث خطأ أثناء تحميل المنتجات');
        }
    },

    async loadLandingPages() {
        try {
            const pages = await safeApiCall('../php/api/landing-pages.php');
            this.displayLandingPages(pages);
        } catch (error) {
            console.error('Error loading landing pages:', error);
            notificationManager.showError('حدث خطأ أثناء تحميل صفحات الهبوط');
        }
    },

    displayLandingPages(pages) {
        const container = document.getElementById('landingPagesContainer');
        if (!container) return;

        if (!Array.isArray(pages) || pages.length === 0) {
            container.innerHTML = '<p class="no-data">لا توجد صفحات هبوط</p>';
            return;
        }

        container.innerHTML = pages.map(page => `
            <div class="landing-page-card">
                <h3>${page.title || 'صفحة هبوط'}</h3>
                <p>المنتج: ${page.product_name || 'غير محدد'}</p>
                <div class="page-actions">
                    <button onclick="landingPagesManager.editPage(${page.id})" class="edit-btn">
                        <i class="fas fa-edit"></i> تعديل
                    </button>
                    <button onclick="landingPagesManager.deletePage(${page.id})" class="delete-btn">
                        <i class="fas fa-trash"></i> حذف
                    </button>
                    <button onclick="copyPageUrl('${page.url}', this)" class="copy-btn">
                        <i class="fas fa-copy"></i> نسخ الرابط
                    </button>
                </div>
            </div>
        `).join('');
    },

    openModal() {
        console.log('Opening modal');
        
        if (!this.modal) {
            console.error('Modal element not found');
            return;
        }
        
        // Show modal with proper display
        this.modal.style.display = 'block';
        this.modal.style.opacity = '1';
        this.modal.style.visibility = 'visible';
        
        // Reset form
        if (this.form) {
            this.form.reset();
        }
        
        // Clear image preview
        if (this.imagePreview) {
            this.imagePreview.innerHTML = '';
        }
        
        // Initialize TinyMCE editors for the modal
        this.initModalTinyMCE();
        
        console.log('Modal opened successfully');
    },

    closeModal() {
        if (!this.modal) return;
        
        this.modal.style.display = 'none';
        this.modal.style.opacity = '0';
        this.modal.style.visibility = 'hidden';
        
        // Clean up TinyMCE instances
        this.cleanupTinyMCE();
        
        console.log('Modal closed');
    },

    initModalTinyMCE() {
        console.log('Initializing TinyMCE for landing pages modal');
        
        // Clean up existing instances first
        this.cleanupTinyMCE();
        
        // Wait a bit for DOM to be ready
        setTimeout(() => {
            const rightContent = document.getElementById('rightContent');
            const leftContent = document.getElementById('leftContent');
            
            if (!rightContent || !leftContent) {
                console.error('TinyMCE target elements not found');
                return;
            }
            
            tinymce.init({
                selector: '#rightContent, #leftContent',
                license_key: 'gpl',
                directionality: 'rtl',
                language: 'ar',
                height: 300,
                readonly: false,
                plugins: 'lists link image media table code',
                toolbar: 'undo redo | formatselect | bold italic | alignright aligncenter alignleft | bullist numlist | link image media | table | code',
                content_css: 'css/tinymce-content.css',
                content_style: 'body { font-family: "Noto Sans Arabic", sans-serif; direction: rtl; text-align: right; }',
                promotion: false,
                branding: false,
                menubar: false,
                setup: function(editor) {
                    editor.on('init', function() {
                        console.log('TinyMCE initialized for:', editor.id);
                        editor.getContainer().style.direction = 'rtl';
                    });
                }
            }).catch(function(error) {
                console.error('TinyMCE initialization failed for landing pages:', error);
            });
        }, 200);
    },

    cleanupTinyMCE() {
        if (typeof tinymce !== 'undefined') {
            try {
                const rightEditor = tinymce.get('rightContent');
                const leftEditor = tinymce.get('leftContent');
                
                if (rightEditor) {
                    rightEditor.remove();
                }
                if (leftEditor) {
                    leftEditor.remove();
                }
            } catch (error) {
                console.warn('Error cleaning up TinyMCE:', error);
            }
        }
    },

    handleImageUpload(event) {
        const files = event.target.files;
        this.imagePreview.innerHTML = '';

        Array.from(files).forEach(file => {
            const reader = new FileReader();
            reader.onload = (e) => {
                const preview = document.createElement('div');
                preview.className = 'preview-image';
                preview.style.backgroundImage = `url(${e.target.result})`;

                const removeBtn = document.createElement('button');
                removeBtn.className = 'remove-image';
                removeBtn.innerHTML = '<i class="fas fa-times"></i>';
                removeBtn.onclick = () => preview.remove();

                preview.appendChild(removeBtn);
                this.imagePreview.appendChild(preview);
            };
            reader.readAsDataURL(file);
        });
    },

    async handleSubmit(event) {
        event.preventDefault();

        const formData = new FormData(this.form);
        
        // Get TinyMCE content safely
        try {
            const rightEditor = tinymce.get('rightContent');
            const leftEditor = tinymce.get('leftContent');
            
            if (rightEditor) {
                formData.append('rightContent', rightEditor.getContent());
            }
            if (leftEditor) {
                formData.append('leftContent', leftEditor.getContent());
            }
        } catch (error) {
            console.warn('Error getting TinyMCE content:', error);
        }

        try {
            const response = await fetch('../php/api/landing-pages.php', {
                method: 'POST',
                body: formData
            });
            const result = await response.json();

            if (result.success) {
                notificationManager.showSuccess('تم إنشاء صفحة الهبوط بنجاح');
                this.closeModal();
                this.loadLandingPages();
            } else {
                notificationManager.showError(result.message || 'حدث خطأ أثناء إنشاء صفحة الهبوط');
            }
        } catch (error) {
            console.error('Error submitting form:', error);
            notificationManager.showError('حدث خطأ أثناء إنشاء صفحة الهبوط');
        }
    },

    async editPage(id) {
        // Implementation for editing landing page
        console.log('Edit page:', id);
    },

    async deletePage(id) {
        if (confirm('هل أنت متأكد من حذف صفحة الهبوط هذه؟')) {
            try {
                const response = await fetch(`../php/api/landing-pages.php?id=${id}`, {
                    method: 'DELETE'
                });
                const result = await response.json();

                if (result.success) {
                    notificationManager.showSuccess('تم حذف صفحة الهبوط بنجاح');
                    this.loadLandingPages();
                } else {
                    notificationManager.showError(result.message || 'حدث خطأ أثناء حذف صفحة الهبوط');
                }
            } catch (error) {
                console.error('Error deleting landing page:', error);
                notificationManager.showError('حدث خطأ أثناء حذف صفحة الهبوط');
            }
        }
    }
};

// Social sharing functions
function sharePage(platform, url) {
    const title = encodeURIComponent(document.title);
    const encodedUrl = encodeURIComponent(url);

    const shareUrls = {
        facebook: `https://www.facebook.com/sharer/sharer.php?u=${encodedUrl}`,
        twitter: `https://twitter.com/intent/tweet?url=${encodedUrl}`,
        whatsapp: `https://api.whatsapp.com/send?text=${title}%20${encodedUrl}`,
        telegram: `https://t.me/share/url?url=${encodedUrl}`
    };

    if (shareUrls[platform]) {
        window.open(shareUrls[platform], '_blank', 'width=600,height=400');
    }
}

// Copy URL to clipboard with visual feedback
function copyPageUrl(url, buttonElement) {
    navigator.clipboard.writeText(url).then(() => {
        notificationManager.showSuccess('تم نسخ الرابط بنجاح');
        if (buttonElement) {
            const originalHtml = buttonElement.innerHTML;
            buttonElement.innerHTML = '<i class="fas fa-check"></i> تم النسخ';
            setTimeout(() => {
                buttonElement.innerHTML = originalHtml;
            }, 2000);
        }
    }).catch(() => {
        notificationManager.showError('فشل في نسخ الرابط');
    });
}

// Make landingPagesManager globally accessible
window.landingPagesManager = landingPagesManager;

// Single initialization - prevent duplicates
if (!window.landingPagesInitialized) {
    window.landingPagesInitialized = true;
    
    // Initialize when DOM is loaded or immediately if already loaded
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', () => {
            landingPagesManager.init();
        });
    } else {
        landingPagesManager.init();
    }
}
