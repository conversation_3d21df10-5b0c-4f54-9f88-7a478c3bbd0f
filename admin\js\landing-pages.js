// Use the existing notificationManager from admin.js

// Initialize landingPagesManager after DOM content is loaded
let landingPagesManager = {
    init() {
        console.log('Initializing Landing Pages Manager');
        this.modal = document.getElementById('landingPageModal');
        this.form = document.getElementById('landingPageForm');
        this.productSelect = document.getElementById('productSelect');
        this.imagePreview = document.getElementById('imagePreview');
        this.addButton = document.getElementById('addLandingPageBtn');
        
        this.bindEvents();
        this.loadProducts();
        this.loadLandingPages();
        this.initTinyMCE();
    },

    bindEvents() {
        console.log('Binding events');
        this.addButton.addEventListener('click', () => {
            console.log('Add button clicked');
            this.openModal();
        });
        this.form.addEventListener('submit', (e) => this.handleSubmit(e));
        document.getElementById('landingPageImages').addEventListener('change', (e) => this.handleImageUpload(e));

        // إضافة مستمعي الأحداث لإغلاق النافذة المنبثقة
        const closeButton = this.modal.querySelector('.cancel-button');
        closeButton.addEventListener('click', () => this.closeModal());

        // إغلاق النافذة المنبثقة عند النقر خارجها
        window.addEventListener('click', (event) => {
            if (event.target === this.modal) {
                this.closeModal();
            }
        });
    },

    async loadProducts() {
        try {
            const response = await fetch('../php/api/products.php');
            const products = await response.json();
            
            this.productSelect.innerHTML = '<option value="">اختر منتجاً</option>';
            products.forEach(product => {
                const option = document.createElement('option');
                option.value = product.id;
                option.textContent = product.titre;
                this.productSelect.appendChild(option);
            });
        } catch (error) {
            console.error('Error loading products:', error);
            notificationManager.showError('حدث خطأ أثناء تحميل المنتجات');
        }
    },

    initTinyMCE() {
        console.log('Initializing TinyMCE');
        tinymce.init({
            selector: '#rightContent, #leftContent',
            directionality: 'rtl',
            language: 'ar',
            height: 300,
            plugins: 'directionality rtl lists link image media table',
            toolbar: 'undo redo | formatselect | bold italic | alignright aligncenter alignleft | bullist numlist | link image media | table',
            content_css: '../admin/css/tinymce-content.css'
        });
    },

    openModal() {
        console.log('Opening modal');
        this.modal.style.display = 'block';
        this.form.reset();
        this.imagePreview.innerHTML = '';
        tinymce.get('rightContent').setContent('');
        tinymce.get('leftContent').setContent('');
    },

    closeModal() {
        this.modal.style.display = 'none';
    },

    handleImageUpload(event) {
        const files = event.target.files;
        this.imagePreview.innerHTML = '';
        
        Array.from(files).forEach(file => {
            const reader = new FileReader();
            reader.onload = (e) => {
                const preview = document.createElement('div');
                preview.className = 'preview-image';
                preview.style.backgroundImage = `url(${e.target.result})`;
                
                const removeBtn = document.createElement('button');
                removeBtn.className = 'remove-image';
                removeBtn.innerHTML = '<i class="fas fa-times"></i>';
                removeBtn.onclick = () => preview.remove();
                
                preview.appendChild(removeBtn);
                this.imagePreview.appendChild(preview);
            };
            reader.readAsDataURL(file);
        });
    },

    async handleSubmit(event) {
        event.preventDefault();
        
        const formData = new FormData(this.form);
        formData.append('rightContent', tinymce.get('rightContent').getContent());
        formData.append('leftContent', tinymce.get('leftContent').getContent());

        try {
            const response = await fetch('../php/api/landing-pages.php', {
                method: 'POST',
                body: formData
            });

            const result = await response.json();
            
            if (result.success) {
                notificationManager.showSuccess('تم إنشاء صفحة الهبوط بنجاح');
                this.closeModal();
                this.loadLandingPages();
            } else {
                notificationManager.showError(result.message || 'حدث خطأ أثناء إنشاء صفحة الهبوط');
            }
        } catch (error) {
            console.error('Error creating landing page:', error);
            notificationManager.showError('حدث خطأ أثناء إنشاء صفحة الهبوط');
        }
    },

    async loadLandingPages() {
        try {
            const response = await fetch('../php/api/landing-pages.php');
            const pages = await response.json();
            
            const tbody = document.querySelector('#landingPagesTable tbody');
            tbody.innerHTML = '';
            
            pages.forEach(page => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${page.product_title}</td>
                    <td>${page.titre}</td>
                    <td>
                        <div class="share-buttons">
                            <a href="${page.lien_url}" target="_blank" class="view-button">
                                <i class="fas fa-eye"></i>
                            </a>
                            <button class="share-button facebook" onclick="sharePage('facebook', '${page.lien_url}')">
                                <i class="fab fa-facebook-f"></i>
                            </button>
                            <button class="share-button whatsapp" onclick="sharePage('whatsapp', '${page.lien_url}')">
                                <i class="fab fa-whatsapp"></i>
                            </button>
                            <button class="share-button copy" onclick="copyPageUrl('${page.lien_url}')">
                                <i class="fas fa-link"></i>
                            </button>
                        </div>
                    </td>
                    <td>
                        <button class="edit-button" onclick="landingPagesManager.editPage(${page.id})">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="delete-button" onclick="landingPagesManager.deletePage(${page.id})">
                            <i class="fas fa-trash"></i>
                        </button>
                    </td>
                `;
                tbody.appendChild(row);
            });
        } catch (error) {
            console.error('Error loading landing pages:', error);
            notificationManager.showError('حدث خطأ أثناء تحميل صفحات الهبوط');
        }
    },

    async editPage(id) {
        // Implementation for editing landing page
    },

    async deletePage(id) {
        if (confirm('هل أنت متأكد من حذف صفحة الهبوط هذه؟')) {
            try {
                const response = await fetch(`../php/api/landing-pages.php?id=${id}`, {
                    method: 'DELETE'
                });
                const result = await response.json();
                
                if (result.success) {
                    notificationManager.showSuccess('تم حذف صفحة الهبوط بنجاح');
                    this.loadLandingPages();
                } else {
                    notificationManager.showError(result.message || 'حدث خطأ أثناء حذف صفحة الهبوط');
                }
            } catch (error) {
                console.error('Error deleting landing page:', error);
                notificationManager.showError('حدث خطأ أثناء حذف صفحة الهبوط');
            }
        }
    }
};

// Make landingPagesManager globally accessible
window.landingPagesManager = landingPagesManager;

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    landingPagesManager.init();
});

// Social sharing functions
function sharePage(platform, url) {
    const title = encodeURIComponent(document.title);
    const encodedUrl = encodeURIComponent(url);
    
    const shareUrls = {
        facebook: `https://www.facebook.com/sharer/sharer.php?u=${encodedUrl}`,
        whatsapp: `https://api.whatsapp.com/send?text=${title}%20${encodedUrl}`
    };
    
    window.open(shareUrls[platform], '_blank');
}

// Copy URL to clipboard with visual feedback
function copyPageUrl(url) {
    navigator.clipboard.writeText(url).then(() => {
        notificationManager.showSuccess('تم نسخ الرابط بنجاح');
        const copyBtn = event.target.closest('.copy');
        if (copyBtn) {
            const originalHtml = copyBtn.innerHTML;
            copyBtn.innerHTML = '<i class="fas fa-check"></i> تم النسخ';
            setTimeout(() => {
                copyBtn.innerHTML = originalHtml;
            }, 2000);
        }
    }).catch(() => {
        notificationManager.showError('حدث خطأ أثناء نسخ الرابط');
    });
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    landingPagesManager.init();
});

// Social sharing functions
function sharePage(platform, url) {
    const title = encodeURIComponent(document.title);
    const encodedUrl = encodeURIComponent(url);
    
    const shareUrls = {
        facebook: `https://www.facebook.com/sharer/sharer.php?u=${encodedUrl}`,
        whatsapp: `https://api.whatsapp.com/send?text=${title}%20${encodedUrl}`
    };
    
    window.open(shareUrls[platform], '_blank');
}

// Copy URL to clipboard with visual feedback
function copyPageUrl(url) {
    navigator.clipboard.writeText(url).then(() => {
        notificationManager.showSuccess('تم نسخ الرابط بنجاح');
        const copyBtn = event.target.closest('.copy');
        if (copyBtn) {
            const originalHtml = copyBtn.innerHTML;
            copyBtn.innerHTML = '<i class="fas fa-check"></i> تم النسخ';
            setTimeout(() => {
                copyBtn.innerHTML = originalHtml;
            }, 2000);
        }
    }).catch(() => {
        notificationManager.showError('حدث خطأ أثناء نسخ الرابط');
    });
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    landingPagesManager.init();
});

// Social sharing functions
function sharePage(platform, url) {
    const title = encodeURIComponent(document.title);
    const encodedUrl = encodeURIComponent(url);
    
    const shareUrls = {
        facebook: `https://www.facebook.com/sharer/sharer.php?u=${encodedUrl}`,
        whatsapp: `https://api.whatsapp.com/send?text=${title}%20${encodedUrl}`
    };
    
    window.open(shareUrls[platform], '_blank');
}

// Copy URL to clipboard with visual feedback
function copyPageUrl(url) {
    navigator.clipboard.writeText(url).then(() => {
        notificationManager.showSuccess('تم نسخ الرابط بنجاح');
        const copyBtn = event.target.closest('.copy');
        if (copyBtn) {
            const originalHtml = copyBtn.innerHTML;
            copyBtn.innerHTML = '<i class="fas fa-check"></i> تم النسخ';
            setTimeout(() => {
                copyBtn.innerHTML = originalHtml;
            }, 2000);
        }
    }).catch(() => {
        notificationManager.showError('حدث خطأ أثناء نسخ الرابط');
    });
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    landingPagesManager.init();
});

// Social sharing functions
function sharePage(platform, url) {
    const title = encodeURIComponent(document.title);
    const encodedUrl = encodeURIComponent(url);
    
    const shareUrls = {
        facebook: `https://www.facebook.com/sharer/sharer.php?u=${encodedUrl}`,
        whatsapp: `https://api.whatsapp.com/send?text=${title}%20${encodedUrl}`
    };
    
    window.open(shareUrls[platform], '_blank');
}

// Copy URL to clipboard with visual feedback
function copyPageUrl(url) {
    navigator.clipboard.writeText(url).then(() => {
        notificationManager.showSuccess('تم نسخ الرابط بنجاح');
        const copyBtn = event.target.closest('.copy');
        if (copyBtn) {
            const originalHtml = copyBtn.innerHTML;
            copyBtn.innerHTML = '<i class="fas fa-check"></i> تم النسخ';
            setTimeout(() => {
                copyBtn.innerHTML = originalHtml;
            }, 2000);
        }
    }).catch(() => {
        notificationManager.showError('حدث خطأ أثناء نسخ الرابط');
    });
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    landingPagesManager.init();
});

// Social sharing functions
function sharePage(platform, url) {
    const title = encodeURIComponent(document.title);
    const encodedUrl = encodeURIComponent(url);
    
    const shareUrls = {
        facebook: `https://www.facebook.com/sharer/sharer.php?u=${encodedUrl}`,
        whatsapp: `https://api.whatsapp.com/send?text=${title}%20${encodedUrl}`
    };
    
    window.open(shareUrls[platform], '_blank');
}

// Copy URL to clipboard with visual feedback
function copyPageUrl(url) {
    navigator.clipboard.writeText(url).then(() => {
        notificationManager.showSuccess('تم نسخ الرابط بنجاح');
        const copyBtn = event.target.closest('.copy');
        if (copyBtn) {
            const originalHtml = copyBtn.innerHTML;
            copyBtn.innerHTML = '<i class="fas fa-check"></i> تم النسخ';
            setTimeout(() => {
                copyBtn.innerHTML = originalHtml;
            }, 2000);
        }
    }).catch(() => {
        notificationManager.showError('حدث خطأ أثناء نسخ الرابط');
    });
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    landingPagesManager.init();
});

// Social sharing functions
function sharePage(platform, url) {
    const title = encodeURIComponent(document.title);
    const encodedUrl = encodeURIComponent(url);
    
    const shareUrls = {
        facebook: `https://www.facebook.com/sharer/sharer.php?u=${encodedUrl}`,
        whatsapp: `https://api.whatsapp.com/send?text=${title}%20${encodedUrl}`
    };
    
    window.open(shareUrls[platform], '_blank');
}

// Copy URL to clipboard with visual feedback
function copyPageUrl(url) {
    navigator.clipboard.writeText(url).then(() => {
        notificationManager.showSuccess('تم نسخ الرابط بنجاح');
        const copyBtn = event.target.closest('.copy');
        if (copyBtn) {
            const originalHtml = copyBtn.innerHTML;
            copyBtn.innerHTML = '<i class="fas fa-check"></i> تم النسخ';
            setTimeout(() => {
                copyBtn.innerHTML = originalHtml;
            }, 2000);
        }
    }).catch(() => {
        notificationManager.showError('حدث خطأ أثناء نسخ الرابط');
    });
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    landingPagesManager.init();
});

// Social sharing functions
function sharePage(platform, url) {
    const title = encodeURIComponent(document.title);
    const encodedUrl = encodeURIComponent(url);
    
    const shareUrls = {
        facebook: `https://www.facebook.com/sharer/sharer.php?u=${encodedUrl}`,
        whatsapp: `https://api.whatsapp.com/send?text=${title}%20${encodedUrl}`
    };
    
    window.open(shareUrls[platform], '_blank');
}

// Copy URL to clipboard with visual feedback
function copyPageUrl(url) {
    navigator.clipboard.writeText(url).then(() => {
        notificationManager.showSuccess('تم نسخ الرابط بنجاح');
        const copyBtn = event.target.closest('.copy');
        if (copyBtn) {
            const originalHtml = copyBtn.innerHTML;
            copyBtn.innerHTML = '<i class="fas fa-check"></i> تم النسخ';
            setTimeout(() => {
                copyBtn.innerHTML = originalHtml;
            }, 2000);
        }
    }).catch(() => {
        notificationManager.showError('حدث خطأ أثناء نسخ الرابط');
    });
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    landingPagesManager.init();
});

// Social sharing functions
function sharePage(platform, url) {
    const title = encodeURIComponent(document.title);
    const encodedUrl = encodeURIComponent(url);
    
    const shareUrls = {
        facebook: `https://www.facebook.com/sharer/sharer.php?u=${encodedUrl}`,
        whatsapp: `https://api.whatsapp.com/send?text=${title}%20${encodedUrl}`
    };
    
    window.open(shareUrls[platform], '_blank');
}

// Copy URL to clipboard with visual feedback
function copyPageUrl(url) {
    navigator.clipboard.writeText(url).then(() => {
        notificationManager.showSuccess('تم نسخ الرابط بنجاح');
        const copyBtn = event.target.closest('.copy');
        if (copyBtn) {
            const originalHtml = copyBtn.innerHTML;
            copyBtn.innerHTML = '<i class="fas fa-check"></i> تم النسخ';
            setTimeout(() => {
                copyBtn.innerHTML = originalHtml;
            }, 2000);
        }
    }).catch(() => {
        notificationManager.showError('حدث خطأ أثناء نسخ الرابط');
    });
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    landingPagesManager.init();
});

// Social sharing functions
function sharePage(platform, url) {
    const title = encodeURIComponent(document.title);
    const encodedUrl = encodeURIComponent(url);
    
    const shareUrls = {
        facebook: `https://www.facebook.com/sharer/sharer.php?u=${encodedUrl}`,
        whatsapp: `https://api.whatsapp.com/send?text=${title}%20${encodedUrl}`
    };
    
    window.open(shareUrls[platform], '_blank');
}

// Copy URL to clipboard with visual feedback
function copyPageUrl(url) {
    navigator.clipboard.writeText(url).then(() => {
        notificationManager.showSuccess('تم نسخ الرابط بنجاح');
        const copyBtn = event.target.closest('.copy');
        if (copyBtn) {
            const originalHtml = copyBtn.innerHTML;
            copyBtn.innerHTML = '<i class="fas fa-check"></i> تم النسخ';
            setTimeout(() => {
                copyBtn.innerHTML = originalHtml;
            }, 2000);
        }
    }).catch(() => {
        notificationManager.showError('حدث خطأ أثناء نسخ الرابط');
    });
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    landingPagesManager.init();
});

// Social sharing functions
function sharePage(platform, url) {
    const title = encodeURIComponent(document.title);
    const encodedUrl = encodeURIComponent(url);
    
    const shareUrls = {
        facebook: `https://www.facebook.com/sharer/sharer.php?u=${encodedUrl}`,
        whatsapp: `https://api.whatsapp.com/send?text=${title}%20${encodedUrl}`
    };
    
    window.open(shareUrls[platform], '_blank');
}

// Copy URL to clipboard with visual feedback
function copyPageUrl(url) {
    navigator.clipboard.writeText(url).then(() => {
        notificationManager.showSuccess('تم نسخ الرابط بنجاح');
        const copyBtn = event.target.closest('.copy');
        if (copyBtn) {
            const originalHtml = copyBtn.innerHTML;
            copyBtn.innerHTML = '<i class="fas fa-check"></i> تم النسخ';
            setTimeout(() => {
                copyBtn.innerHTML = originalHtml;
            }, 2000);
        }
    }).catch(() => {
        notificationManager.showError('حدث خطأ أثناء نسخ الرابط');
    });
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    landingPagesManager.init();
});

// Social sharing functions
function sharePage(platform, url) {
    const title = encodeURIComponent(document.title);
    const encodedUrl = encodeURIComponent(url);
    
    const shareUrls = {
        facebook: `https://www.facebook.com/sharer/sharer.php?u=${encodedUrl}`,
        whatsapp: `https://api.whatsapp.com/send?text=${title}%20${encodedUrl}`
    };
    
    window.open(shareUrls[platform], '_blank');
}

// Copy URL to clipboard with visual feedback
function copyPageUrl(url) {
    navigator.clipboard.writeText(url).then(() => {
        notificationManager.showSuccess('تم نسخ الرابط بنجاح');
        const copyBtn = event.target.closest('.copy');
        if (copyBtn) {
            const originalHtml = copyBtn.innerHTML;
            copyBtn.innerHTML = '<i class="fas fa-check"></i> تم النسخ';
            setTimeout(() => {
                copyBtn.innerHTML = originalHtml;
            }, 2000);
        }
    }).catch(() => {
        notificationManager.showError('حدث خطأ أثناء نسخ الرابط');
    });
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    landingPagesManager.init();
});

// Social sharing functions
function sharePage(platform, url) {
    const title = encodeURIComponent(document.title);
    const encodedUrl = encodeURIComponent(url);
    
    const shareUrls = {
        facebook: `https://www.facebook.com/sharer/sharer.php?u=${encodedUrl}`,
        whatsapp: `https://api.whatsapp.com/send?text=${title}%20${encodedUrl}`
    };
    
    window.open(shareUrls[platform], '_blank');
}

// Copy URL to clipboard with visual feedback
function copyPageUrl(url) {
    navigator.clipboard.writeText(url).then(() => {
        notificationManager.showSuccess('تم نسخ الرابط بنجاح');
        const copyBtn = event.target.closest('.copy');
        if (copyBtn) {
            const originalHtml = copyBtn.innerHTML;
            copyBtn.innerHTML = '<i class="fas fa-check"></i> تم النسخ';
            setTimeout(() => {
                copyBtn.innerHTML = originalHtml;
            }, 2000);
        }
    }).catch(() => {
        notificationManager.showError('حدث خطأ أثناء نسخ الرابط');
    });
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    landingPagesManager.init();
});

// Social sharing functions
function sharePage(platform, url) {
    const title = encodeURIComponent(document.title);
    const encodedUrl = encodeURIComponent(url);
    
    const shareUrls = {
        facebook: `https://www.facebook.com/sharer/sharer.php?u=${encodedUrl}`,
        whatsapp: `https://api.whatsapp.com/send?text=${title}%20${encodedUrl}`
    };
    
    window.open(shareUrls[platform], '_blank');
}

// Copy URL to clipboard with visual feedback
function copyPageUrl(url) {
    navigator.clipboard.writeText(url).then(() => {
        notificationManager.showSuccess('تم نسخ الرابط بنجاح');
        const copyBtn = event.target.closest('.copy');
        if (copyBtn) {
            const originalHtml = copyBtn.innerHTML;
            copyBtn.innerHTML = '<i class="fas fa-check"></i> تم النسخ';
            setTimeout(() => {
                copyBtn.innerHTML = originalHtml;
            }, 2000);
        }
    }).catch(() => {
        notificationManager.showError('حدث خطأ أثناء نسخ الرابط');
    });
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    landingPagesManager.init();
});

// Social sharing functions
function sharePage(platform, url) {
    const title = encodeURIComponent(document.title);
    const encodedUrl = encodeURIComponent(url);
    
    const shareUrls = {
        facebook: `https://www.facebook.com/sharer/sharer.php?u=${encodedUrl}`,
        whatsapp: `https://api.whatsapp.com/send?text=${title}%20${encodedUrl}`
    };
    
    window.open(shareUrls[platform], '_blank');
}

// Copy URL to clipboard with visual feedback
function copyPageUrl(url) {
    navigator.clipboard.writeText(url).then(() => {
        notificationManager.showSuccess('تم نسخ الرابط بنجاح');
        const copyBtn = event.target.closest('.copy');
        if (copyBtn) {
            const originalHtml = copyBtn.innerHTML;
            copyBtn.innerHTML = '<i class="fas fa-check"></i> تم النسخ';
            setTimeout(() => {
                copyBtn.innerHTML = originalHtml;
            }, 2000);
        }
    }).catch(() => {
        notificationManager.showError('حدث خطأ أثناء نسخ الرابط');
    });
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    landingPagesManager.init();
});

// Social sharing functions
function sharePage(platform, url) {
    const title = encodeURIComponent(document.title);
    const encodedUrl = encodeURIComponent(url);
    
    const shareUrls = {
        facebook: `https://www.facebook.com/sharer/sharer.php?u=${encodedUrl}`,
        whatsapp: `https://api.whatsapp.com/send?text=${title}%20${encodedUrl}`
    };
    
    window.open(shareUrls[platform], '_blank');
}

// Copy URL to clipboard with visual feedback
function copyPageUrl(url) {
    navigator.clipboard.writeText(url).then(() => {
        notificationManager.showSuccess('تم نسخ الرابط بنجاح');
        const copyBtn = event.target.closest('.copy');
        if (copyBtn) {
            const originalHtml = copyBtn.innerHTML;
            copyBtn.innerHTML = '<i class="fas fa-check"></i> تم النسخ';
            setTimeout(() => {
                copyBtn.innerHTML = originalHtml;
            }, 2000);
        }
    }).catch(() => {
        notificationManager.showError('حدث خطأ أثناء نسخ الرابط');
    });
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    landingPagesManager.init();
});

// Social sharing functions
function sharePage(platform, url) {
    const title = encodeURIComponent(document.title);
    const encodedUrl = encodeURIComponent(url);
    
    const shareUrls = {
        facebook: `https://www.facebook.com/sharer/sharer.php?u=${encodedUrl}`,
        whatsapp: `https://api.whatsapp.com/send?text=${title}%20${encodedUrl}`
    };
    
    window.open(shareUrls[platform], '_blank');
}

// Copy URL to clipboard with visual feedback
function copyPageUrl(url) {
    navigator.clipboard.writeText(url).then(() => {
        notificationManager.showSuccess('تم نسخ الرابط بنجاح');
        const copyBtn = event.target.closest('.copy');
        if (copyBtn) {
            const originalHtml = copyBtn.innerHTML;
            copyBtn.innerHTML = '<i class="fas fa-check"></i> تم النسخ';
            setTimeout(() => {
                copyBtn.innerHTML = originalHtml;
            }, 2000);
        }
    }).catch(() => {
        notificationManager.showError('حدث خطأ أثناء نسخ الرابط');
    });
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    landingPagesManager.init();
});

// Social sharing functions
function sharePage(platform, url) {
    const title = encodeURIComponent(document.title);
    const encodedUrl = encodeURIComponent(url);
    
    const shareUrls = {
        facebook: `https://www.facebook.com/sharer/sharer.php?u=${encodedUrl}`,
        whatsapp: `https://api.whatsapp.com/send?text=${title}%20${encodedUrl}`
    };
    
    window.open(shareUrls[platform], '_blank');
}

// Copy URL to clipboard with visual feedback
function copyPageUrl(url) {
    navigator.clipboard.writeText(url).then(() => {
        notificationManager.showSuccess('تم نسخ الرابط بنجاح');
        const copyBtn = event.target.closest('.copy');
        if (copyBtn) {
            const originalHtml = copyBtn.innerHTML;
            copyBtn.innerHTML = '<i class="fas fa-check"></i> تم النسخ';
            setTimeout(() => {
                copyBtn.innerHTML = originalHtml;
            }, 2000);
        }
    }).catch(() => {
        notificationManager.showError('حدث خطأ أثناء نسخ الرابط');
    });
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    landingPagesManager.init();
});

// Social sharing functions
function sharePage(platform, url) {
    const title = encodeURIComponent(document.title);
    const encodedUrl = encodeURIComponent(url);
    
    const shareUrls = {
        facebook: `https://www.facebook.com/sharer/sharer.php?u=${encodedUrl}`,
        whatsapp: `https://api.whatsapp.com/send?text=${title}%20${encodedUrl}`
    };
    
    window.open(shareUrls[platform], '_blank');
}

// Copy URL to clipboard with visual feedback
function copyPageUrl(url) {
    navigator.clipboard.writeText(url).then(() => {
        notificationManager.showSuccess('تم نسخ الرابط بنجاح');
        const copyBtn = event.target.closest('.copy');
        if (copyBtn) {
            const originalHtml = copyBtn.innerHTML;
            copyBtn.innerHTML = '<i class="fas fa-check"></i> تم النسخ';
            setTimeout(() => {
                copyBtn.innerHTML = originalHtml;
            }, 2000);
        }
    }).catch(() => {
        notificationManager.showError('حدث خطأ أثناء نسخ الرابط');
    });
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    landingPagesManager.init();
});

// Social sharing functions
function sharePage(platform, url) {
    const title = encodeURIComponent(document.title);
    const encodedUrl = encodeURIComponent(url);
    
    const shareUrls = {
        facebook: `https://www.facebook.com/sharer/sharer.php?u=${encodedUrl}`,
        whatsapp: `https://api.whatsapp.com/send?text=${title}%20${encodedUrl}`
    };
    
    window.open(shareUrls[platform], '_blank');
}

// Copy URL to clipboard with visual feedback
function copyPageUrl(url) {
    navigator.clipboard.writeText(url).then(() => {
        notificationManager.showSuccess('تم نسخ الرابط بنجاح');
        const copyBtn = event.target.closest('.copy');
        if (copyBtn) {
            const originalHtml = copyBtn.innerHTML;
            copyBtn.innerHTML = '<i class="fas fa-check"></i> تم النسخ';
            setTimeout(() => {
                copyBtn.innerHTML = originalHtml;
            }, 2000);
        }
    }).catch(() => {
        notificationManager.showError('حدث خطأ أثناء نسخ الرابط');
    });
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    landingPagesManager.init();
});

// Social sharing functions
function sharePage(platform, url) {
    const title = encodeURIComponent(document.title);
    const encodedUrl = encodeURIComponent(url);
    
    const shareUrls = {
        facebook: `https://www.facebook.com/sharer/sharer.php?u=${encodedUrl}`,
        whatsapp: `https://api.whatsapp.com/send?text=${title}%20${encodedUrl}`
    };
    
    window.open(shareUrls[platform], '_blank');
}

// Copy URL to clipboard with visual feedback
function copyPageUrl(url) {
    navigator.clipboard.writeText(url).then(() => {
        notificationManager.showSuccess('تم نسخ الرابط بنجاح');
        const copyBtn = event.target.closest('.copy');
        if (copyBtn) {
            const originalHtml = copyBtn.innerHTML;
            copyBtn.innerHTML = '<i class="fas fa-check"></i> تم النسخ';
            setTimeout(() => {
                copyBtn.innerHTML = originalHtml;
            }, 2000);
        }
    }).catch(() => {
        notificationManager.showError('حدث خطأ أثناء نسخ الرابط');
    });
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    landingPagesManager.init();
});

// Social sharing functions
function sharePage(platform, url) {
    const title = encodeURIComponent(document.title);
    const encodedUrl = encodeURIComponent(url);
    
    const shareUrls = {
        facebook: `https://www.facebook.com/sharer/sharer.php?u=${encodedUrl}`,
        whatsapp: `https://api.whatsapp.com/send?text=${title}%20${encodedUrl}`
    };
    
    window.open(shareUrls[platform], '_blank');
}

// Copy URL to clipboard with visual feedback
function copyPageUrl(url) {
    navigator.clipboard.writeText(url).then(() => {
        notificationManager.showSuccess('تم نسخ الرابط بنجاح');
        const copyBtn = event.target.closest('.copy');
        if (copyBtn) {
            const originalHtml = copyBtn.innerHTML;
            copyBtn.innerHTML = '<i class="fas fa-check"></i> تم النسخ';
            setTimeout(() => {
                copyBtn.innerHTML = originalHtml;
            }, 2000);
        }
    }).catch(() => {
        notificationManager.showError('حدث خطأ أثناء نسخ الرابط');
    });
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    landingPagesManager.init();
});

// Social sharing functions
function sharePage(platform, url) {
    const title = encodeURIComponent(document.title);
    const encodedUrl = encodeURIComponent(url);
    
    const shareUrls = {
        facebook: `https://www.facebook.com/sharer/sharer.php?u=${encodedUrl}`,
        whatsapp: `https://api.whatsapp.com/send?text=${title}%20${encodedUrl}`
    };
    
    window.open(shareUrls[platform], '_blank');
}

// Copy URL to clipboard with visual feedback
function copyPageUrl(url) {
    navigator.clipboard.writeText(url).then(() => {
        notificationManager.showSuccess('تم نسخ الرابط بنجاح');
        const copyBtn = event.target.closest('.copy');
        if (copyBtn) {
            const originalHtml = copyBtn.innerHTML;
            copyBtn.innerHTML = '<i class="fas fa-check"></i> تم النسخ';
            setTimeout(() => {
                copyBtn.innerHTML = originalHtml;
            }, 2000);
        }
    }).catch(() => {
        notificationManager.showError('حدث خطأ أثناء نسخ الرابط');
    });
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    landingPagesManager.init();
});

// Social sharing functions
function sharePage(platform, url) {
    const title = encodeURIComponent(document.title);
    const encodedUrl = encodeURIComponent(url);
    
    const shareUrls = {
        facebook: `https://www.facebook.com/sharer/sharer.php?u=${encodedUrl}`,
        whatsapp: `https://api.whatsapp.com/send?text=${title}%20${encodedUrl}`
    };
    
    window.open(shareUrls[platform], '_blank');
}

// Copy URL to clipboard with visual feedback
function copyPageUrl(url) {
    navigator.clipboard.writeText(url).then(() => {
        notificationManager.showSuccess('تم نسخ الرابط بنجاح');
        const copyBtn = event.target.closest('.copy');
        if (copyBtn) {
            const originalHtml = copyBtn.innerHTML;
            copyBtn.innerHTML = '<i class="fas fa-check"></i> تم النسخ';
            setTimeout(() => {
                copyBtn.innerHTML = originalHtml;
            }, 2000);
        }
    }).catch(() => {
        notificationManager.showError('حدث خطأ أثناء نسخ الرابط');
    });
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    landingPagesManager.init();
});

// Social sharing functions
function sharePage(platform, url) {
    const title = encodeURIComponent(document.title);
    const encodedUrl = encodeURIComponent(url);
    
    const shareUrls = {
        facebook: `https://www.facebook.com/sharer/sharer.php?u=${encodedUrl}`,
        whatsapp: `https://api.whatsapp.com/send?text=${title}%20${encodedUrl}`
    };
    
    window.open(shareUrls[platform], '_blank');
}

// Copy URL to clipboard with visual feedback
function copyPageUrl(url) {
    navigator.clipboard.writeText(url).then(() => {
        notificationManager.showSuccess('تم نسخ الرابط بنجاح');
        const copyBtn = event.target.closest('.copy');
        if (copyBtn) {
            const originalHtml = copyBtn.innerHTML;
            copyBtn.innerHTML = '<i class="fas fa-check"></i> تم النسخ';
            setTimeout(() => {
                copyBtn.innerHTML = originalHtml;
            }, 2000);
        }
    }).catch(() => {
        notificationManager.showError('حدث خطأ أثناء نسخ الرابط');
    });
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    landingPagesManager.init();
});

// Social sharing functions
function sharePage(platform, url) {
    const title = encodeURIComponent(document.title);
    const encodedUrl = encodeURIComponent(url);
    
    const shareUrls = {
        facebook: `https://www.facebook.com/sharer/sharer.php?u=${encodedUrl}`,
        whatsapp: `https://api.whatsapp.com/send?text=${title}%20${encodedUrl}`
    };
    
    window.open(shareUrls[platform], '_blank');
}

// Copy URL to clipboard with visual feedback
function copyPageUrl(url) {
    navigator.clipboard.writeText(url).then(() => {
        notificationManager.showSuccess('تم نسخ الرابط بنجاح');
        const copyBtn = event.target.closest('.copy');
        if (copyBtn) {
            const originalHtml = copyBtn.innerHTML;
            copyBtn.innerHTML = '<i class="fas fa-check"></i> تم النسخ';
            setTimeout(() => {
                copyBtn.innerHTML = originalHtml;
            }, 2000);
        }
    }).catch(() => {
        notificationManager.showError('حدث خطأ أثناء نسخ الرابط');
    });
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    landingPagesManager.init();
});

// Social sharing functions
function sharePage(platform, url) {
    const title = encodeURIComponent(document.title);
    const encodedUrl = encodeURIComponent(url);
    
    const shareUrls = {
        facebook: `https://www.facebook.com/sharer/sharer.php?u=${encodedUrl}`,
        whatsapp: `https://api.whatsapp.com/send?text=${title}%20${encodedUrl}`
    };
    
    window.open(shareUrls[platform], '_blank');
}

// Copy URL to clipboard with visual feedback
function copyPageUrl(url) {
    navigator.clipboard.writeText(url).then(() => {
        notificationManager.showSuccess('تم نسخ الرابط بنجاح');
        const copyBtn = event.target.closest('.copy');
        if (copyBtn) {
            const originalHtml = copyBtn.innerHTML;
            copyBtn.innerHTML = '<i class="fas fa-check"></i> تم النسخ';
            setTimeout(() => {
                copyBtn.innerHTML = originalHtml;
            }, 2000);
        }
    }).catch(() => {
        notificationManager.showError('حدث خطأ أثناء نسخ الرابط');
    });
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    landingPagesManager.init();
});

// Social sharing functions
function sharePage(platform, url) {
    const title = encodeURIComponent(document.title);
    const encodedUrl = encodeURIComponent(url);
    
    const shareUrls = {
        facebook: `https://www.facebook.com/sharer/sharer.php?u=${encodedUrl}`,
        whatsapp: `https://api.whatsapp.com/send?text=${title}%20${encodedUrl}`
    };
    
    window.open(shareUrls[platform], '_blank');
}

// Copy URL to clipboard with visual feedback
function copyPageUrl(url) {
    navigator.clipboard.writeText(url).then(() => {
        notificationManager.showSuccess('تم نسخ الرابط بنجاح');
        const copyBtn = event.target.closest('.copy');
        if (copyBtn) {
            const originalHtml = copyBtn.innerHTML;
            copyBtn.innerHTML = '<i class="fas fa-check"></i> تم النسخ';
            setTimeout(() => {
                copyBtn.innerHTML = originalHtml;
            }, 2000);
        }
    }).catch(() => {
        notificationManager.showError('حدث خطأ أثناء نسخ الرابط');
    });
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    landingPagesManager.init();
});

// Social sharing functions
function sharePage(platform, url) {
    const title = encodeURIComponent(document.title);
    const encodedUrl = encodeURIComponent(url);
    
    const shareUrls = {
        facebook: `https://www.facebook.com/sharer/sharer.php?u=${encodedUrl}`,
        whatsapp: `https://api.whatsapp.com/send?text=${title}%20${encodedUrl}`
    };
    
    window.open(shareUrls[platform], '_blank');
}

// Copy URL to clipboard with visual feedback
function copyPageUrl(url) {
    navigator.clipboard.writeText(url).then(() => {
        notificationManager.showSuccess('تم نسخ الرابط بنجاح');
        const copyBtn = event.target.closest('.copy');
        if (copyBtn) {
            const originalHtml = copyBtn.innerHTML;
            copyBtn.innerHTML = '<i class="fas fa-check"></i> تم النسخ';
            setTimeout(() => {
                copyBtn.innerHTML = originalHtml;
            }, 2000);
        }
    }).catch(() => {
        notificationManager.showError('حدث خطأ أثناء نسخ الرابط');
    });
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    landingPagesManager.init();
});

// Social sharing functions
function sharePage(platform, url) {
    const title = encodeURIComponent(document.title);
    const encodedUrl = encodeURIComponent(url);
    
    const shareUrls = {
        facebook: `https://www.facebook.com/sharer/sharer.php?u=${encodedUrl}`,
        whatsapp: `https://api.whatsapp.com/send?text=${title}%20${encodedUrl}`
    };
    
    window.open(shareUrls[platform], '_blank');
}

// Copy URL to clipboard with visual feedback
function copyPageUrl(url) {
    navigator.clipboard.writeText(url).then(() => {
        notificationManager.showSuccess('تم نسخ الرابط بنجاح');
        const copyBtn = event.target.closest('.copy');
        if (copyBtn) {
            const originalHtml = copyBtn.innerHTML;
            copyBtn.innerHTML = '<i class="fas fa-check"></i> تم النسخ';
            setTimeout(() => {
                copyBtn.innerHTML = originalHtml;
            }, 2000);
        }
    }).catch(() => {
        notificationManager.showError('حدث خطأ أثناء نسخ الرابط');
    });
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    landingPagesManager.init();
});

// Social sharing functions
function sharePage(platform, url) {
    const title = encodeURIComponent(document.title);
    const encodedUrl = encodeURIComponent(url);
    
    const shareUrls = {
        facebook: `https://www.facebook.com/sharer/sharer.php?u=${encodedUrl}`,
        whatsapp: `https://api.whatsapp.com/send?text=${title}%20${encodedUrl}`
    };
    
    window.open(shareUrls[platform], '_blank');
}

// Copy URL to clipboard with visual feedback
function copyPageUrl(url) {
    navigator.clipboard.writeText(url).then(() => {
        notificationManager.showSuccess('تم نسخ الرابط بنجاح');
        const copyBtn = event.target.closest('.copy');
        if (copyBtn) {
            const originalHtml = copyBtn.innerHTML;
            copyBtn.innerHTML = '<i class="fas fa-check"></i> تم النسخ';
            setTimeout(() => {
                copyBtn.innerHTML = originalHtml;
            }, 2000);
        }
    }).catch(() => {
        notificationManager.showError('حدث خطأ أثناء نسخ الرابط');
    });
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    landingPagesManager.init();
});

// Social sharing functions
function sharePage(platform, url) {
    const title = encodeURIComponent(document.title);
    const encodedUrl = encodeURIComponent(url);
    
    const shareUrls = {
        facebook: `https://www.facebook.com/sharer/sharer.php?u=${encodedUrl}`,
        whatsapp: `https://api.whatsapp.com/send?text=${title}%20${encodedUrl}`
    };
    
    window.open(shareUrls[platform], '_blank');
}

// Copy URL to clipboard with visual feedback
function copyPageUrl(url) {
    navigator.clipboard.writeText(url).then(() => {
        notificationManager.showSuccess('تم نسخ الرابط بنجاح');
        const copyBtn = event.target.closest('.copy');
        if (copyBtn) {
            const originalHtml = copyBtn.innerHTML;
            copyBtn.innerHTML = '<i class="fas fa-check"></i> تم النسخ';
            setTimeout(() => {
                copyBtn.innerHTML = originalHtml;
            }, 2000);
        }
    }).catch(() => {
        notificationManager.showError('حدث خطأ أثناء نسخ الرابط');
    });
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    landingPagesManager.init();
});

// Social sharing functions
function sharePage(platform, url) {
    const title = encodeURIComponent(document.title);
    const encodedUrl = encodeURIComponent(url);
    
    const shareUrls = {
        facebook: `https://www.facebook.com/sharer/sharer.php?u=${encodedUrl}`,
        whatsapp: `https://api.whatsapp.com/send?text=${title}%20${encodedUrl}`
    };
    
    window.open(shareUrls[platform], '_blank');
}

// Copy URL to clipboard with visual feedback
function copyPageUrl(url) {
    navigator.clipboard.writeText(url).then(() => {
        notificationManager.showSuccess('تم نسخ الرابط بنجاح');
        const copyBtn = event.target.closest('.copy');
        if (copyBtn) {
            const originalHtml = copyBtn.innerHTML;
            copyBtn.innerHTML = '<i class="fas fa-check"></i> تم النسخ';
            setTimeout(() => {
                copyBtn.innerHTML = originalHtml;
            }, 2000);
        }
    }).catch(() => {
        notificationManager.showError('حدث خطأ أثناء نسخ الرابط');
    });
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    landingPagesManager.init();
});

// Social sharing functions
function sharePage(platform, url) {
    const title = encodeURIComponent(document.title);
    const encodedUrl = encodeURIComponent(url);
    
    const shareUrls = {
        facebook: `https://www.facebook.com/sharer/sharer.php?u=${encodedUrl}`,
        whatsapp: `https://api.whatsapp.com/send?text=${title}%20${encodedUrl}`
    };
    
    window.open(shareUrls[platform], '_blank');
}

// Copy URL to clipboard with visual feedback
function copyPageUrl(url) {
    navigator.clipboard.writeText(url).then(() => {
        notificationManager.showSuccess('تم نسخ الرابط بنجاح');
        const copyBtn = event.target.closest('.copy');
        if (copyBtn) {
            const originalHtml = copyBtn.innerHTML;
            copyBtn.innerHTML = '<i class="fas fa-check"></i> تم النسخ';
            setTimeout(() => {
                copyBtn.innerHTML = originalHtml;
            }, 2000);
        }
    }).catch(() => {
        notificationManager.showError('حدث خطأ أثناء نسخ الرابط');
    });
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    landingPagesManager.init();
});

// Social sharing functions
function sharePage(platform, url) {
    const title = encodeURIComponent(document.title);
    const encodedUrl = encodeURIComponent(url);
    
    const shareUrls = {
        facebook: `https://www.facebook.com/sharer/sharer.php?u=${encodedUrl}`,
        whatsapp: `https://api.whatsapp.com/send?text=${title}%20${encodedUrl}`
    };
    
    window.open(shareUrls[platform], '_blank');
}

// Copy URL to clipboard with visual feedback
function copyPageUrl(url) {
    navigator.clipboard.writeText(url).then(() => {
        notificationManager.showSuccess('تم نسخ الرابط بنجاح');
        const copyBtn = event.target.closest('.copy');
        if (copyBtn) {
            const originalHtml = copyBtn.innerHTML;
            copyBtn.innerHTML = '<i class="fas fa-check"></i> تم النسخ';
            setTimeout(() => {
                copyBtn.innerHTML = originalHtml;
            }, 2000);
        }
    }).catch(() => {
        notificationManager.showError('حدث خطأ أثناء نسخ الرابط');
    });
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    landingPagesManager.init();
});

// Social sharing functions
function sharePage(platform, url) {
    const title = encodeURIComponent(document.title);
    const encodedUrl = encodeURIComponent(url);
    
    const shareUrls = {
        facebook: `https://www.facebook.com/sharer/sharer.php?u=${encodedUrl}`,
        whatsapp: `https://api.whatsapp.com/send?text=${title}%20${encodedUrl}`
    };
    
    window.open(shareUrls[platform], '_blank');
}

// Copy URL to clipboard with visual feedback
function copyPageUrl(url) {
    navigator.clipboard.writeText(url).then(() => {
        notificationManager.showSuccess('تم نسخ الرابط بنجاح');
        const copyBtn = event.target.closest('.copy');
        if (copyBtn) {
            const originalHtml = copyBtn.innerHTML;
            copyBtn.innerHTML = '<i class="fas fa-check"></i> تم النسخ';
            setTimeout(() => {
                copyBtn.innerHTML = originalHtml;
            }, 2000);
        }
    }).catch(() => {
        notificationManager.showError('حدث خطأ أثناء نسخ الرابط');
    });
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    landingPagesManager.init();
});

// Social sharing functions
function sharePage(platform, url) {
    const title = encodeURIComponent(document.title);
    const encodedUrl = encodeURIComponent(url);
    
    const shareUrls = {
        facebook: `https://www.facebook.com/sharer/sharer.php?u=${encodedUrl}`,
        whatsapp: `https://api.whatsapp.com/send?text=${title}%20${encodedUrl}`
    };
    
    window.open(shareUrls[platform], '_blank');
}

// Copy URL to clipboard with visual feedback
function copyPageUrl(url) {
    navigator.clipboard.writeText(url).then(() => {
        notificationManager.showSuccess('تم نسخ الرابط بنجاح');
        const copyBtn = event.target.closest('.copy');
        if (copyBtn) {
            const originalHtml = copyBtn.innerHTML;
            copyBtn.innerHTML = '<i class="fas fa-check"></i> تم النسخ';
            setTimeout(() => {
                copyBtn.innerHTML = originalHtml;
            }, 2000);
        }
    }).catch(() => {
        notificationManager.showError('حدث خطأ أثناء نسخ الرابط');
    });
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    landingPagesManager.init();
});

// Social sharing functions
function sharePage(platform, url) {
    const title = encodeURIComponent(document.title);
    const encodedUrl = encodeURIComponent(url);
    
    const shareUrls = {
        facebook: `https://www.facebook.com/sharer/sharer.php?u=${encodedUrl}`,
        whatsapp: `https://api.whatsapp.com/send?text=${title}%20${encodedUrl}`
    };
    
    window.open(shareUrls[platform], '_blank');
}

// Copy URL to clipboard with visual feedback
function copyPageUrl(url) {
    navigator.clipboard.writeText(url).then(() => {
        notificationManager.showSuccess('تم نسخ الرابط بنجاح');
        const copyBtn = event.target.closest('.copy');
        if (copyBtn) {
            const originalHtml = copyBtn.innerHTML;
            copyBtn.innerHTML = '<i class="fas fa-check"></i> تم النسخ';
            setTimeout(() => {
                copyBtn.innerHTML = originalHtml;
            }, 2000);
        }
    }).catch(() => {
        notificationManager.showError('حدث خطأ أثناء نسخ الرابط');
    });
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    landingPagesManager.init();
});

// Social sharing functions
function sharePage(platform, url) {
    const title = encodeURIComponent(document.title);
    const encodedUrl = encodeURIComponent(url);
    
    const shareUrls = {
        facebook: `https://www.facebook.com/sharer/sharer.php?u=${encodedUrl}`,
        whatsapp: `https://api.whatsapp.com/send?text=${title}%20${encodedUrl}`
    };
    
    window.open(shareUrls[platform], '_blank');
}

// Copy URL to clipboard with visual feedback
function copyPageUrl(url) {
    navigator.clipboard.writeText(url).then(() => {
        notificationManager.showSuccess('تم نسخ الرابط بنجاح');
        const copyBtn = event.target.closest('.copy');
        if (copyBtn) {
            const originalHtml = copyBtn.innerHTML;
            copyBtn.innerHTML = '<i class="fas fa-check"></i> تم النسخ';
            setTimeout(() => {
                copyBtn.innerHTML = originalHtml;
            }, 2000);
        }
    }).catch(() => {
        notificationManager.showError('حدث خطأ أثناء نسخ الرابط');
    });
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    landingPagesManager.init();
});

// Social sharing functions
function sharePage(platform, url) {
    const title = encodeURIComponent(document.title);
    const encodedUrl = encodeURIComponent(url);
    
    const shareUrls = {
        facebook: `https://www.facebook.com/sharer/sharer.php?u=${encodedUrl}`,
        whatsapp: `https://api.whatsapp.com/send?text=${title}%20${encodedUrl}`
    };
    
    window.open(shareUrls[platform], '_blank');
}

// Copy URL to clipboard with visual feedback
function copyPageUrl(url) {
    navigator.clipboard.writeText(url).then(() => {
        notificationManager.showSuccess('تم نسخ الرابط بنجاح');
        const copyBtn = event.target.closest('.copy');
        if (copyBtn) {
            const originalHtml = copyBtn.innerHTML;
            copyBtn.innerHTML = '<i class="fas fa-check"></i> تم النسخ';
            setTimeout(() => {
                copyBtn.innerHTML = originalHtml;
            }, 2000);
        }
    }).catch(() => {
        notificationManager.showError('حدث خطأ أثناء نسخ الرابط');
    });
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    landingPagesManager.init();
});

// Social sharing functions
function sharePage(platform, url) {
    const title = encodeURIComponent(document.title);
    const encodedUrl = encodeURIComponent(url);
    
    const shareUrls = {
        facebook: `https://www.facebook.com/sharer/sharer.php?u=${encodedUrl}`,
        whatsapp: `https://api.whatsapp.com/send?text=${title}%20${encodedUrl}`
    };
    
    window.open(shareUrls[platform], '_blank');
}

// Copy URL to clipboard with visual feedback
function copyPageUrl(url) {
    navigator.clipboard.writeText(url).then(() => {
        notificationManager.showSuccess('تم نسخ الرابط بنجاح');
        const copyBtn = event.target.closest('.copy');
        if (copyBtn) {
            const originalHtml = copyBtn.innerHTML;
            copyBtn.innerHTML = '<i class="fas fa-check"></i> تم النسخ';
            setTimeout(() => {
                copyBtn.innerHTML = originalHtml;
            }, 2000);
        }
    }).catch(() => {
        notificationManager.showError('حدث خطأ أثناء نسخ الرابط');
    });
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    landingPagesManager.init();
});

// Social sharing functions
function sharePage(platform, url) {
    const title = encodeURIComponent(document.title);
    const encodedUrl = encodeURIComponent(url);
    
    const shareUrls = {
        facebook: `https://www.facebook.com/sharer/sharer.php?u=${encodedUrl}`,
        whatsapp: `https://api.whatsapp.com/send?text=${title}%20${encodedUrl}`
    };
    
    window.open(shareUrls[platform], '_blank');
}

// Copy URL to clipboard with visual feedback
function copyPageUrl(url) {
    navigator.clipboard.writeText(url).then(() => {
        notificationManager.showSuccess('تم نسخ الرابط بنجاح');
        const copyBtn = event.target.closest('.copy');
        if (copyBtn) {
            const originalHtml = copyBtn.innerHTML;
            copyBtn.innerHTML = '<i class="fas fa-check"></i> تم النسخ';
            setTimeout(() => {
                copyBtn.innerHTML = originalHtml;
            }, 2000);
        }
    }).catch(() => {
        notificationManager.showError('