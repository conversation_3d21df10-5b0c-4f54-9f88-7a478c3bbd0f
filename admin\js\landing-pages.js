// Use the existing notificationManager from admin.js

// Utility function for safe API calls with proper error handling
async function safeApiCall(url, options = {}) {
    try {
        const response = await fetch(url, options);

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const text = await response.text();

        // Check if response is empty
        if (!text.trim()) {
            console.warn('Empty response from API:', url);
            return [];
        }

        // Try to parse JSON
        try {
            return JSON.parse(text);
        } catch (parseError) {
            console.error('JSON parse error for response:', text);
            throw new Error('Invalid JSON response from server');
        }
    } catch (error) {
        console.error('API call failed:', error);
        throw error;
    }
}

// Utility function for safe DOM selection handling
function safeGetSelection() {
    try {
        const selection = window.getSelection();
        if (selection && typeof selection.rangeCount !== 'undefined') {
            return selection;
        }
        return null;
    } catch (error) {
        console.warn('Selection access failed:', error);
        return null;
    }
}

// Initialize landingPagesManager after DOM content is loaded
let landingPagesManager = {
    init() {
        console.log('Initializing Landing Pages Manager');

        // Check if elements exist before proceeding
        this.modal = document.getElementById('landingPageModal');
        this.form = document.getElementById('landingPageForm');
        this.productSelect = document.getElementById('productSelect');
        this.imagePreview = document.getElementById('imagePreview');
        this.addButton = document.getElementById('addLandingPageBtn');

        if (!this.modal || !this.form || !this.productSelect || !this.addButton) {
            console.error('Required elements not found for landing pages manager');
            console.log('Elements found:', {
                modal: !!this.modal,
                form: !!this.form,
                productSelect: !!this.productSelect,
                addButton: !!this.addButton
            });
            return;
        }

        this.bindEvents();
        this.loadProducts();
        this.loadLandingPages();
    },

    bindEvents() {
        console.log('Binding events');

        // Add button click handler
        if (this.addButton) {
            this.addButton.addEventListener('click', (e) => {
                e.preventDefault();
                console.log('Add landing page button clicked');
                this.openModal();
            });
        }

        // Form submit handler
        if (this.form) {
            this.form.addEventListener('submit', (e) => this.handleSubmit(e));
        }

        // Image upload handler
        const imageInput = document.getElementById('landingPageImages');
        if (imageInput) {
            imageInput.addEventListener('change', (e) => this.handleImageUpload(e));
        }

        // Close button handler
        const closeButton = this.modal ? this.modal.querySelector('.cancel-button') : null;
        if (closeButton) {
            closeButton.addEventListener('click', (e) => {
                e.preventDefault();
                this.closeModal();
            });
        }

        // Close modal when clicking outside
        if (this.modal) {
            window.addEventListener('click', (event) => {
                if (event.target === this.modal) {
                    this.closeModal();
                }
            });
        }
    },

    async loadProducts() {
        try {
            const products = await safeApiCall('../php/api/products.php');

            this.productSelect.innerHTML = '<option value="">اختر منتجاً</option>';

            if (Array.isArray(products)) {
                products.forEach(product => {
                    const option = document.createElement('option');
                    option.value = product.id;
                    option.textContent = product.titre || product.title || `Product ${product.id}`;
                    this.productSelect.appendChild(option);
                });
            }
        } catch (error) {
            console.error('Error loading products:', error);
            notificationManager.showError('حدث خطأ أثناء تحميل المنتجات');
        }
    },

    initTinyMCE() {
        console.log('Initializing TinyMCE for landing pages');

        // Remove existing instances first
        if (typeof tinymce !== 'undefined') {
            tinymce.remove('#rightContent');
            tinymce.remove('#leftContent');
        }

        tinymce.init({
            selector: '#rightContent, #leftContent',
            license_key: 'gpl',
            directionality: 'rtl',
            language: 'ar',
            height: 300,
            readonly: false,
            plugins: 'lists link image media table code',
            toolbar: 'undo redo | formatselect | bold italic | alignright aligncenter alignleft | bullist numlist | link image media | table | code',
            content_css: 'css/tinymce-content.css',
            content_style: 'body { font-family: "Noto Sans Arabic", sans-serif; direction: rtl; text-align: right; }',
            promotion: false,
            branding: false,
            menubar: false,
            setup: function(editor) {
                editor.on('init', function() {
                    console.log('TinyMCE initialized for:', editor.id);
                    editor.getContainer().style.direction = 'rtl';
                });
            }
        }).catch(function(error) {
            console.error('TinyMCE initialization failed for landing pages:', error);
        });
    },

    openModal() {
        console.log('Opening modal');
        this.modal.style.display = 'block';
        this.form.reset();
        this.imagePreview.innerHTML = '';

        // Initialize TinyMCE if not already done
        setTimeout(() => {
            this.initTinyMCE();

            // Clear content after a short delay to ensure editors are ready
            setTimeout(() => {
                const rightEditor = tinymce.get('rightContent');
                const leftEditor = tinymce.get('leftContent');

                if (rightEditor) {
                    rightEditor.setContent('');
                }
                if (leftEditor) {
                    leftEditor.setContent('');
                }
            }, 500);
        }, 100);
    },

    closeModal() {
        this.modal.style.display = 'none';
    },

    handleImageUpload(event) {
        const files = event.target.files;
        this.imagePreview.innerHTML = '';

        Array.from(files).forEach(file => {
            const reader = new FileReader();
            reader.onload = (e) => {
                const preview = document.createElement('div');
                preview.className = 'preview-image';
                preview.style.backgroundImage = `url(${e.target.result})`;

                const removeBtn = document.createElement('button');
                removeBtn.className = 'remove-image';
                removeBtn.innerHTML = '<i class="fas fa-times"></i>';
                removeBtn.onclick = () => preview.remove();

                preview.appendChild(removeBtn);
                this.imagePreview.appendChild(preview);
            };
            reader.readAsDataURL(file);
        });
    },

    async handleSubmit(event) {
        event.preventDefault();

        const formData = new FormData(this.form);
        formData.append('rightContent', tinymce.get('rightContent').getContent());
        formData.append('leftContent', tinymce.get('leftContent').getContent());

        try {
            const response = await fetch('../php/api/landing-pages.php', {
                method: 'POST',
                body: formData
            });

            const result = await response.json();

            if (result.success) {
                notificationManager.showSuccess('تم إنشاء صفحة الهبوط بنجاح');
                this.closeModal();
                this.loadLandingPages();
            } else {
                notificationManager.showError(result.message || 'حدث خطأ أثناء إنشاء صفحة الهبوط');
            }
        } catch (error) {
            console.error('Error creating landing page:', error);
            notificationManager.showError('حدث خطأ أثناء إنشاء صفحة الهبوط');
        }
    },

    async loadLandingPages() {
        try {
            const response = await fetch('../php/api/landing-pages.php');
            const pages = await response.json();

            const tbody = document.querySelector('#landingPagesTable tbody');
            tbody.innerHTML = '';

            pages.forEach(page => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${page.product_title}</td>
                    <td>${page.titre}</td>
                    <td>
                        <div class="share-buttons">
                            <a href="${page.lien_url}" target="_blank" class="view-button">
                                <i class="fas fa-eye"></i>
                            </a>
                            <button class="share-button facebook" onclick="sharePage('facebook', '${page.lien_url}')">
                                <i class="fab fa-facebook-f"></i>
                            </button>
                            <button class="share-button whatsapp" onclick="sharePage('whatsapp', '${page.lien_url}')">
                                <i class="fab fa-whatsapp"></i>
                            </button>
                            <button class="share-button copy" onclick="copyPageUrl('${page.lien_url}')">
                                <i class="fas fa-link"></i>
                            </button>
                        </div>
                    </td>
                    <td>
                        <button class="edit-button" onclick="landingPagesManager.editPage(${page.id})">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="delete-button" onclick="landingPagesManager.deletePage(${page.id})">
                            <i class="fas fa-trash"></i>
                        </button>
                    </td>
                `;
                tbody.appendChild(row);
            });
        } catch (error) {
            console.error('Error loading landing pages:', error);
            notificationManager.showError('حدث خطأ أثناء تحميل صفحات الهبوط');
        }
    },

    async editPage(id) {
        // Implementation for editing landing page
    },

    async deletePage(id) {
        if (confirm('هل أنت متأكد من حذف صفحة الهبوط هذه؟')) {
            try {
                const response = await fetch(`../php/api/landing-pages.php?id=${id}`, {
                    method: 'DELETE'
                });
                const result = await response.json();

                if (result.success) {
                    notificationManager.showSuccess('تم حذف صفحة الهبوط بنجاح');
                    this.loadLandingPages();
                } else {
                    notificationManager.showError(result.message || 'حدث خطأ أثناء حذف صفحة الهبوط');
                }
            } catch (error) {
                console.error('Error deleting landing page:', error);
                notificationManager.showError('حدث خطأ أثناء حذف صفحة الهبوط');
            }
        }
    }
};

// Make landingPagesManager globally accessible
window.landingPagesManager = landingPagesManager;

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    landingPagesManager.init();
});

// Social sharing functions
function sharePage(platform, url) {
    const title = encodeURIComponent(document.title);
    const encodedUrl = encodeURIComponent(url);

    const shareUrls = {
        facebook: `https://www.facebook.com/sharer/sharer.php?u=${encodedUrl}`,
        whatsapp: `https://api.whatsapp.com/send?text=${title}%20${encodedUrl}`
    };

    window.open(shareUrls[platform], '_blank');
}

// Copy URL to clipboard with visual feedback
function copyPageUrl(url) {
    navigator.clipboard.writeText(url).then(() => {
        notificationManager.showSuccess('تم نسخ الرابط بنجاح');
        const copyBtn = event.target.closest('.copy');
        if (copyBtn) {
            const originalHtml = copyBtn.innerHTML;
            copyBtn.innerHTML = '<i class="fas fa-check"></i> تم النسخ';
            setTimeout(() => {
                copyBtn.innerHTML = originalHtml;
            }, 2000);
        }
    }).catch(() => {
        notificationManager.showError('حدث خطأ أثناء نسخ الرابط');
    });
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    landingPagesManager.init();
});

// Social sharing functions
function sharePage(platform, url) {
    const title = encodeURIComponent(document.title);
    const encodedUrl = encodeURIComponent(url);

    const shareUrls = {
        facebook: `https://www.facebook.com/sharer/sharer.php?u=${encodedUrl}`,
        whatsapp: `https://api.whatsapp.com/send?text=${title}%20${encodedUrl}`
    };

    window.open(shareUrls[platform], '_blank');
}

// Copy URL to clipboard with visual feedback
function copyPageUrl(url) {
    navigator.clipboard.writeText(url).then(() => {
        notificationManager.showSuccess('تم نسخ الرابط بنجاح');
        const copyBtn = event.target.closest('.copy');
        if (copyBtn) {
            const originalHtml = copyBtn.innerHTML;
            copyBtn.innerHTML = '<i class="fas fa-check"></i> تم النسخ';
            setTimeout(() => {
                copyBtn.innerHTML = originalHtml;
            }, 2000);
        }
    }).catch(() => {
        notificationManager.showError('حدث خطأ أثناء نسخ الرابط');
    });
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    landingPagesManager.init();
});

// Social sharing functions
function sharePage(platform, url) {
    const title = encodeURIComponent(document.title);
    const encodedUrl = encodeURIComponent(url);

    const shareUrls = {
        facebook: `https://www.facebook.com/sharer/sharer.php?u=${encodedUrl}`,
        whatsapp: `https://api.whatsapp.com/send?text=${title}%20${encodedUrl}`
    };

    window.open(shareUrls[platform], '_blank');
}

// Copy URL to clipboard with visual feedback
function copyPageUrl(url) {
    navigator.clipboard.writeText(url).then(() => {
        notificationManager.showSuccess('تم نسخ الرابط بنجاح');
        const copyBtn = event.target.closest('.copy');
        if (copyBtn) {
            const originalHtml = copyBtn.innerHTML;
            copyBtn.innerHTML = '<i class="fas fa-check"></i> تم النسخ';
            setTimeout(() => {
                copyBtn.innerHTML = originalHtml;
            }, 2000);
        }
    }).catch(() => {
        notificationManager.showError('حدث خطأ أثناء نسخ الرابط');
    });
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    landingPagesManager.init();
});

// Social sharing functions
function sharePage(platform, url) {
    const title = encodeURIComponent(document.title);
    const encodedUrl = encodeURIComponent(url);

    const shareUrls = {
        facebook: `https://www.facebook.com/sharer/sharer.php?u=${encodedUrl}`,
        whatsapp: `https://api.whatsapp.com/send?text=${title}%20${encodedUrl}`
    };

    window.open(shareUrls[platform], '_blank');
}

// Copy URL to clipboard with visual feedback
function copyPageUrl(url) {
    navigator.clipboard.writeText(url).then(() => {
        notificationManager.showSuccess('تم نسخ الرابط بنجاح');
        const copyBtn = event.target.closest('.copy');
        if (copyBtn) {
            const originalHtml = copyBtn.innerHTML;
            copyBtn.innerHTML = '<i class="fas fa-check"></i> تم النسخ';
            setTimeout(() => {
                copyBtn.innerHTML = originalHtml;
            }, 2000);
        }
    }).catch(() => {
        notificationManager.showError('حدث خطأ أثناء نسخ الرابط');
    });
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    landingPagesManager.init();
});

// Social sharing functions
function sharePage(platform, url) {
    const title = encodeURIComponent(document.title);
    const encodedUrl = encodeURIComponent(url);

    const shareUrls = {
        facebook: `https://www.facebook.com/sharer/sharer.php?u=${encodedUrl}`,
        whatsapp: `https://api.whatsapp.com/send?text=${title}%20${encodedUrl}`
    };

    window.open(shareUrls[platform], '_blank');
}

// Copy URL to clipboard with visual feedback
function copyPageUrl(url) {
    navigator.clipboard.writeText(url).then(() => {
        notificationManager.showSuccess('تم نسخ الرابط بنجاح');
        const copyBtn = event.target.closest('.copy');
        if (copyBtn) {
            const originalHtml = copyBtn.innerHTML;
            copyBtn.innerHTML = '<i class="fas fa-check"></i> تم النسخ';
            setTimeout(() => {
                copyBtn.innerHTML = originalHtml;
            }, 2000);
        }
    }).catch(() => {
        notificationManager.showError('حدث خطأ أثناء نسخ الرابط');
    });
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    landingPagesManager.init();
});

// Social sharing functions
function sharePage(platform, url) {
    const title = encodeURIComponent(document.title);
    const encodedUrl = encodeURIComponent(url);

    const shareUrls = {
        facebook: `https://www.facebook.com/sharer/sharer.php?u=${encodedUrl}`,
        whatsapp: `https://api.whatsapp.com/send?text=${title}%20${encodedUrl}`
    };

    window.open(shareUrls[platform], '_blank');
}

// Copy URL to clipboard with visual feedback
function copyPageUrl(url) {
    navigator.clipboard.writeText(url).then(() => {
        notificationManager.showSuccess('تم نسخ الرابط بنجاح');
        const copyBtn = event.target.closest('.copy');
        if (copyBtn) {
            const originalHtml = copyBtn.innerHTML;
            copyBtn.innerHTML = '<i class="fas fa-check"></i> تم النسخ';
            setTimeout(() => {
                copyBtn.innerHTML = originalHtml;
            }, 2000);
        }
    }).catch(() => {
        notificationManager.showError('حدث خطأ أثناء نسخ الرابط');
    });
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    landingPagesManager.init();
});

// Social sharing functions
function sharePage(platform, url) {
    const title = encodeURIComponent(document.title);
    const encodedUrl = encodeURIComponent(url);

    const shareUrls = {
        facebook: `https://www.facebook.com/sharer/sharer.php?u=${encodedUrl}`,
        whatsapp: `https://api.whatsapp.com/send?text=${title}%20${encodedUrl}`
    };

    window.open(shareUrls[platform], '_blank');
}

// Copy URL to clipboard with visual feedback
function copyPageUrl(url) {
    navigator.clipboard.writeText(url).then(() => {
        notificationManager.showSuccess('تم نسخ الرابط بنجاح');
        const copyBtn = event.target.closest('.copy');
        if (copyBtn) {
            const originalHtml = copyBtn.innerHTML;
            copyBtn.innerHTML = '<i class="fas fa-check"></i> تم النسخ';
            setTimeout(() => {
                copyBtn.innerHTML = originalHtml;
            }, 2000);
        }
    }).catch(() => {
        notificationManager.showError('حدث خطأ أثناء نسخ الرابط');
    });
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    landingPagesManager.init();
});

// Social sharing functions
function sharePage(platform, url) {
    const title = encodeURIComponent(document.title);
    const encodedUrl = encodeURIComponent(url);

    const shareUrls = {
        facebook: `https://www.facebook.com/sharer/sharer.php?u=${encodedUrl}`,
        whatsapp: `https://api.whatsapp.com/send?text=${title}%20${encodedUrl}`
    };

    window.open(shareUrls[platform], '_blank');
}

// Copy URL to clipboard with visual feedback
function copyPageUrl(url) {
    navigator.clipboard.writeText(url).then(() => {
        notificationManager.showSuccess('تم نسخ الرابط بنجاح');
        const copyBtn = event.target.closest('.copy');
        if (copyBtn) {
            const originalHtml = copyBtn.innerHTML;
            copyBtn.innerHTML = '<i class="fas fa-check"></i> تم النسخ';
            setTimeout(() => {
                copyBtn.innerHTML = originalHtml;
            }, 2000);
        }
    }).catch(() => {
        notificationManager.showError('حدث خطأ أثناء نسخ الرابط');
    });
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    landingPagesManager.init();
});

// Social sharing functions
function sharePage(platform, url) {
    const title = encodeURIComponent(document.title);
    const encodedUrl = encodeURIComponent(url);

    const shareUrls = {
        facebook: `https://www.facebook.com/sharer/sharer.php?u=${encodedUrl}`,
        whatsapp: `https://api.whatsapp.com/send?text=${title}%20${encodedUrl}`
    };

    window.open(shareUrls[platform], '_blank');
}

// Copy URL to clipboard with visual feedback
function copyPageUrl(url) {
    navigator.clipboard.writeText(url).then(() => {
        notificationManager.showSuccess('تم نسخ الرابط بنجاح');
        const copyBtn = event.target.closest('.copy');
        if (copyBtn) {
            const originalHtml = copyBtn.innerHTML;
            copyBtn.innerHTML = '<i class="fas fa-check"></i> تم النسخ';
            setTimeout(() => {
                copyBtn.innerHTML = originalHtml;
            }, 2000);
        }
    }).catch(() => {
        notificationManager.showError('حدث خطأ أثناء نسخ الرابط');
    });
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    landingPagesManager.init();
});

// Social sharing functions
function sharePage(platform, url) {
    const title = encodeURIComponent(document.title);
    const encodedUrl = encodeURIComponent(url);

    const shareUrls = {
        facebook: `https://www.facebook.com/sharer/sharer.php?u=${encodedUrl}`,
        whatsapp: `https://api.whatsapp.com/send?text=${title}%20${encodedUrl}`
    };

    window.open(shareUrls[platform], '_blank');
}

// Copy URL to clipboard with visual feedback
function copyPageUrl(url) {
    navigator.clipboard.writeText(url).then(() => {
        notificationManager.showSuccess('تم نسخ الرابط بنجاح');
        const copyBtn = event.target.closest('.copy');
        if (copyBtn) {
            const originalHtml = copyBtn.innerHTML;
            copyBtn.innerHTML = '<i class="fas fa-check"></i> تم النسخ';
            setTimeout(() => {
                copyBtn.innerHTML = originalHtml;
            }, 2000);
        }
    }).catch(() => {
        notificationManager.showError('حدث خطأ أثناء نسخ الرابط');
    });
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    landingPagesManager.init();
});

// Social sharing functions
function sharePage(platform, url) {
    const title = encodeURIComponent(document.title);
    const encodedUrl = encodeURIComponent(url);

    const shareUrls = {
        facebook: `https://www.facebook.com/sharer/sharer.php?u=${encodedUrl}`,
        whatsapp: `https://api.whatsapp.com/send?text=${title}%20${encodedUrl}`
    };

    window.open(shareUrls[platform], '_blank');
}

// Copy URL to clipboard with visual feedback
function copyPageUrl(url) {
    navigator.clipboard.writeText(url).then(() => {
        notificationManager.showSuccess('تم نسخ الرابط بنجاح');
        const copyBtn = event.target.closest('.copy');
        if (copyBtn) {
            const originalHtml = copyBtn.innerHTML;
            copyBtn.innerHTML = '<i class="fas fa-check"></i> تم النسخ';
            setTimeout(() => {
                copyBtn.innerHTML = originalHtml;
            }, 2000);
        }
    }).catch(() => {
        notificationManager.showError('حدث خطأ أثناء نسخ الرابط');
    });
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    landingPagesManager.init();
});

// Social sharing functions
function sharePage(platform, url) {
    const title = encodeURIComponent(document.title);
    const encodedUrl = encodeURIComponent(url);

    const shareUrls = {
        facebook: `https://www.facebook.com/sharer/sharer.php?u=${encodedUrl}`,
        whatsapp: `https://api.whatsapp.com/send?text=${title}%20${encodedUrl}`
    };

    window.open(shareUrls[platform], '_blank');
}

// Copy URL to clipboard with visual feedback
function copyPageUrl(url) {
    navigator.clipboard.writeText(url).then(() => {
        notificationManager.showSuccess('تم نسخ الرابط بنجاح');
        const copyBtn = event.target.closest('.copy');
        if (copyBtn) {
            const originalHtml = copyBtn.innerHTML;
            copyBtn.innerHTML = '<i class="fas fa-check"></i> تم النسخ';
            setTimeout(() => {
                copyBtn.innerHTML = originalHtml;
            }, 2000);
        }
    }).catch(() => {
        notificationManager.showError('حدث خطأ أثناء نسخ الرابط');
    });
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    landingPagesManager.init();
});

// Social sharing functions
function sharePage(platform, url) {
    const title = encodeURIComponent(document.title);
    const encodedUrl = encodeURIComponent(url);

    const shareUrls = {
        facebook: `https://www.facebook.com/sharer/sharer.php?u=${encodedUrl}`,
        whatsapp: `https://api.whatsapp.com/send?text=${title}%20${encodedUrl}`
    };

    window.open(shareUrls[platform], '_blank');
}

// Copy URL to clipboard with visual feedback
function copyPageUrl(url) {
    navigator.clipboard.writeText(url).then(() => {
        notificationManager.showSuccess('تم نسخ الرابط بنجاح');
        const copyBtn = event.target.closest('.copy');
        if (copyBtn) {
            const originalHtml = copyBtn.innerHTML;
            copyBtn.innerHTML = '<i class="fas fa-check"></i> تم النسخ';
            setTimeout(() => {
                copyBtn.innerHTML = originalHtml;
            }, 2000);
        }
    }).catch(() => {
        notificationManager.showError('حدث خطأ أثناء نسخ الرابط');
    });
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    landingPagesManager.init();
});

// Social sharing functions
function sharePage(platform, url) {
    const title = encodeURIComponent(document.title);
    const encodedUrl = encodeURIComponent(url);

    const shareUrls = {
        facebook: `https://www.facebook.com/sharer/sharer.php?u=${encodedUrl}`,
        whatsapp: `https://api.whatsapp.com/send?text=${title}%20${encodedUrl}`
    };

    window.open(shareUrls[platform], '_blank');
}

// Copy URL to clipboard with visual feedback
function copyPageUrl(url) {
    navigator.clipboard.writeText(url).then(() => {
        notificationManager.showSuccess('تم نسخ الرابط بنجاح');
        const copyBtn = event.target.closest('.copy');
        if (copyBtn) {
            const originalHtml = copyBtn.innerHTML;
            copyBtn.innerHTML = '<i class="fas fa-check"></i> تم النسخ';
            setTimeout(() => {
                copyBtn.innerHTML = originalHtml;
            }, 2000);
        }
    }).catch(() => {
        notificationManager.showError('حدث خطأ أثناء نسخ الرابط');
    });
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    landingPagesManager.init();
});

// Social sharing functions
function sharePage(platform, url) {
    const title = encodeURIComponent(document.title);
    const encodedUrl = encodeURIComponent(url);

    const shareUrls = {
        facebook: `https://www.facebook.com/sharer/sharer.php?u=${encodedUrl}`,
        whatsapp: `https://api.whatsapp.com/send?text=${title}%20${encodedUrl}`
    };

    window.open(shareUrls[platform], '_blank');
}

// Copy URL to clipboard with visual feedback
function copyPageUrl(url) {
    navigator.clipboard.writeText(url).then(() => {
        notificationManager.showSuccess('تم نسخ الرابط بنجاح');
        const copyBtn = event.target.closest('.copy');
        if (copyBtn) {
            const originalHtml = copyBtn.innerHTML;
            copyBtn.innerHTML = '<i class="fas fa-check"></i> تم النسخ';
            setTimeout(() => {
                copyBtn.innerHTML = originalHtml;
            }, 2000);
        }
    }).catch(() => {
        notificationManager.showError('حدث خطأ أثناء نسخ الرابط');
    });
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    landingPagesManager.init();
});

// Social sharing functions
function sharePage(platform, url) {
    const title = encodeURIComponent(document.title);
    const encodedUrl = encodeURIComponent(url);

    const shareUrls = {
        facebook: `https://www.facebook.com/sharer/sharer.php?u=${encodedUrl}`,
        whatsapp: `https://api.whatsapp.com/send?text=${title}%20${encodedUrl}`
    };

    window.open(shareUrls[platform], '_blank');
}

// Copy URL to clipboard with visual feedback
function copyPageUrl(url) {
    navigator.clipboard.writeText(url).then(() => {
        notificationManager.showSuccess('تم نسخ الرابط بنجاح');
        const copyBtn = event.target.closest('.copy');
        if (copyBtn) {
            const originalHtml = copyBtn.innerHTML;
            copyBtn.innerHTML = '<i class="fas fa-check"></i> تم النسخ';
            setTimeout(() => {
                copyBtn.innerHTML = originalHtml;
            }, 2000);
        }
    }).catch(() => {
        notificationManager.showError('حدث خطأ أثناء نسخ الرابط');
    });
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    landingPagesManager.init();
});

// Social sharing functions
function sharePage(platform, url) {
    const title = encodeURIComponent(document.title);
    const encodedUrl = encodeURIComponent(url);

    const shareUrls = {
        facebook: `https://www.facebook.com/sharer/sharer.php?u=${encodedUrl}`,
        whatsapp: `https://api.whatsapp.com/send?text=${title}%20${encodedUrl}`
    };

    window.open(shareUrls[platform], '_blank');
}

// Copy URL to clipboard with visual feedback
function copyPageUrl(url) {
    navigator.clipboard.writeText(url).then(() => {
        notificationManager.showSuccess('تم نسخ الرابط بنجاح');
        const copyBtn = event.target.closest('.copy');
        if (copyBtn) {
            const originalHtml = copyBtn.innerHTML;
            copyBtn.innerHTML = '<i class="fas fa-check"></i> تم النسخ';
            setTimeout(() => {
                copyBtn.innerHTML = originalHtml;
            }, 2000);
        }
    }).catch(() => {
        notificationManager.showError('حدث خطأ أثناء نسخ الرابط');
    });
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    landingPagesManager.init();
});

// Social sharing functions
function sharePage(platform, url) {
    const title = encodeURIComponent(document.title);
    const encodedUrl = encodeURIComponent(url);

    const shareUrls = {
        facebook: `https://www.facebook.com/sharer/sharer.php?u=${encodedUrl}`,
        whatsapp: `https://api.whatsapp.com/send?text=${title}%20${encodedUrl}`
    };

    window.open(shareUrls[platform], '_blank');
}

// Copy URL to clipboard with visual feedback
function copyPageUrl(url) {
    navigator.clipboard.writeText(url).then(() => {
        notificationManager.showSuccess('تم نسخ الرابط بنجاح');
        const copyBtn = event.target.closest('.copy');
        if (copyBtn) {
            const originalHtml = copyBtn.innerHTML;
            copyBtn.innerHTML = '<i class="fas fa-check"></i> تم النسخ';
            setTimeout(() => {
                copyBtn.innerHTML = originalHtml;
            }, 2000);
        }
    }).catch(() => {
        notificationManager.showError('حدث خطأ أثناء نسخ الرابط');
    });
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    landingPagesManager.init();
});

// Social sharing functions
function sharePage(platform, url) {
    const title = encodeURIComponent(document.title);
    const encodedUrl = encodeURIComponent(url);

    const shareUrls = {
        facebook: `https://www.facebook.com/sharer/sharer.php?u=${encodedUrl}`,
        whatsapp: `https://api.whatsapp.com/send?text=${title}%20${encodedUrl}`
    };

    window.open(shareUrls[platform], '_blank');
}

// Copy URL to clipboard with visual feedback
function copyPageUrl(url) {
    navigator.clipboard.writeText(url).then(() => {
        notificationManager.showSuccess('تم نسخ الرابط بنجاح');
        const copyBtn = event.target.closest('.copy');
        if (copyBtn) {
            const originalHtml = copyBtn.innerHTML;
            copyBtn.innerHTML = '<i class="fas fa-check"></i> تم النسخ';
            setTimeout(() => {
                copyBtn.innerHTML = originalHtml;
            }, 2000);
        }
    }).catch(() => {
        notificationManager.showError('حدث خطأ أثناء نسخ الرابط');
    });
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    landingPagesManager.init();
});

// Social sharing functions
function sharePage(platform, url) {
    const title = encodeURIComponent(document.title);
    const encodedUrl = encodeURIComponent(url);

    const shareUrls = {
        facebook: `https://www.facebook.com/sharer/sharer.php?u=${encodedUrl}`,
        whatsapp: `https://api.whatsapp.com/send?text=${title}%20${encodedUrl}`
    };

    window.open(shareUrls[platform], '_blank');
}

// Copy URL to clipboard with visual feedback
function copyPageUrl(url) {
    navigator.clipboard.writeText(url).then(() => {
        notificationManager.showSuccess('تم نسخ الرابط بنجاح');
        const copyBtn = event.target.closest('.copy');
        if (copyBtn) {
            const originalHtml = copyBtn.innerHTML;
            copyBtn.innerHTML = '<i class="fas fa-check"></i> تم النسخ';
            setTimeout(() => {
                copyBtn.innerHTML = originalHtml;
            }, 2000);
        }
    }).catch(() => {
        notificationManager.showError('حدث خطأ أثناء نسخ الرابط');
    });
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    landingPagesManager.init();
});

// Social sharing functions
function sharePage(platform, url) {
    const title = encodeURIComponent(document.title);
    const encodedUrl = encodeURIComponent(url);

    const shareUrls = {
        facebook: `https://www.facebook.com/sharer/sharer.php?u=${encodedUrl}`,
        whatsapp: `https://api.whatsapp.com/send?text=${title}%20${encodedUrl}`
    };

    window.open(shareUrls[platform], '_blank');
}

// Copy URL to clipboard with visual feedback
function copyPageUrl(url) {
    navigator.clipboard.writeText(url).then(() => {
        notificationManager.showSuccess('تم نسخ الرابط بنجاح');
        const copyBtn = event.target.closest('.copy');
        if (copyBtn) {
            const originalHtml = copyBtn.innerHTML;
            copyBtn.innerHTML = '<i class="fas fa-check"></i> تم النسخ';
            setTimeout(() => {
                copyBtn.innerHTML = originalHtml;
            }, 2000);
        }
    }).catch(() => {
        notificationManager.showError('حدث خطأ أثناء نسخ الرابط');
    });
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    landingPagesManager.init();
});

// Social sharing functions
function sharePage(platform, url) {
    const title = encodeURIComponent(document.title);
    const encodedUrl = encodeURIComponent(url);

    const shareUrls = {
        facebook: `https://www.facebook.com/sharer/sharer.php?u=${encodedUrl}`,
        whatsapp: `https://api.whatsapp.com/send?text=${title}%20${encodedUrl}`
    };

    window.open(shareUrls[platform], '_blank');
}

// Copy URL to clipboard with visual feedback
function copyPageUrl(url) {
    navigator.clipboard.writeText(url).then(() => {
        notificationManager.showSuccess('تم نسخ الرابط بنجاح');
        const copyBtn = event.target.closest('.copy');
        if (copyBtn) {
            const originalHtml = copyBtn.innerHTML;
            copyBtn.innerHTML = '<i class="fas fa-check"></i> تم النسخ';
            setTimeout(() => {
                copyBtn.innerHTML = originalHtml;
            }, 2000);
        }
    }).catch(() => {
        notificationManager.showError('حدث خطأ أثناء نسخ الرابط');
    });
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    landingPagesManager.init();
});

// Social sharing functions
function sharePage(platform, url) {
    const title = encodeURIComponent(document.title);
    const encodedUrl = encodeURIComponent(url);

    const shareUrls = {
        facebook: `https://www.facebook.com/sharer/sharer.php?u=${encodedUrl}`,
        whatsapp: `https://api.whatsapp.com/send?text=${title}%20${encodedUrl}`
    };

    window.open(shareUrls[platform], '_blank');
}

// Copy URL to clipboard with visual feedback
function copyPageUrl(url) {
    navigator.clipboard.writeText(url).then(() => {
        notificationManager.showSuccess('تم نسخ الرابط بنجاح');
        const copyBtn = event.target.closest('.copy');
        if (copyBtn) {
            const originalHtml = copyBtn.innerHTML;
            copyBtn.innerHTML = '<i class="fas fa-check"></i> تم النسخ';
            setTimeout(() => {
                copyBtn.innerHTML = originalHtml;
            }, 2000);
        }
    }).catch(() => {
        notificationManager.showError('حدث خطأ أثناء نسخ الرابط');
    });
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    landingPagesManager.init();
});

// Social sharing functions
function sharePage(platform, url) {
    const title = encodeURIComponent(document.title);
    const encodedUrl = encodeURIComponent(url);

    const shareUrls = {
        facebook: `https://www.facebook.com/sharer/sharer.php?u=${encodedUrl}`,
        whatsapp: `https://api.whatsapp.com/send?text=${title}%20${encodedUrl}`
    };

    window.open(shareUrls[platform], '_blank');
}

// Copy URL to clipboard with visual feedback
function copyPageUrl(url) {
    navigator.clipboard.writeText(url).then(() => {
        notificationManager.showSuccess('تم نسخ الرابط بنجاح');
        const copyBtn = event.target.closest('.copy');
        if (copyBtn) {
            const originalHtml = copyBtn.innerHTML;
            copyBtn.innerHTML = '<i class="fas fa-check"></i> تم النسخ';
            setTimeout(() => {
                copyBtn.innerHTML = originalHtml;
            }, 2000);
        }
    }).catch(() => {
        notificationManager.showError('حدث خطأ أثناء نسخ الرابط');
    });
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    landingPagesManager.init();
});

// Social sharing functions
function sharePage(platform, url) {
    const title = encodeURIComponent(document.title);
    const encodedUrl = encodeURIComponent(url);

    const shareUrls = {
        facebook: `https://www.facebook.com/sharer/sharer.php?u=${encodedUrl}`,
        whatsapp: `https://api.whatsapp.com/send?text=${title}%20${encodedUrl}`
    };

    window.open(shareUrls[platform], '_blank');
}

// Copy URL to clipboard with visual feedback
function copyPageUrl(url) {
    navigator.clipboard.writeText(url).then(() => {
        notificationManager.showSuccess('تم نسخ الرابط بنجاح');
        const copyBtn = event.target.closest('.copy');
        if (copyBtn) {
            const originalHtml = copyBtn.innerHTML;
            copyBtn.innerHTML = '<i class="fas fa-check"></i> تم النسخ';
            setTimeout(() => {
                copyBtn.innerHTML = originalHtml;
            }, 2000);
        }
    }).catch(() => {
        notificationManager.showError('حدث خطأ أثناء نسخ الرابط');
    });
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    landingPagesManager.init();
});

// Social sharing functions
function sharePage(platform, url) {
    const title = encodeURIComponent(document.title);
    const encodedUrl = encodeURIComponent(url);

    const shareUrls = {
        facebook: `https://www.facebook.com/sharer/sharer.php?u=${encodedUrl}`,
        whatsapp: `https://api.whatsapp.com/send?text=${title}%20${encodedUrl}`
    };

    window.open(shareUrls[platform], '_blank');
}

// Copy URL to clipboard with visual feedback
function copyPageUrl(url) {
    navigator.clipboard.writeText(url).then(() => {
        notificationManager.showSuccess('تم نسخ الرابط بنجاح');
        const copyBtn = event.target.closest('.copy');
        if (copyBtn) {
            const originalHtml = copyBtn.innerHTML;
            copyBtn.innerHTML = '<i class="fas fa-check"></i> تم النسخ';
            setTimeout(() => {
                copyBtn.innerHTML = originalHtml;
            }, 2000);
        }
    }).catch(() => {
        notificationManager.showError('حدث خطأ أثناء نسخ الرابط');
    });
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    landingPagesManager.init();
});

// Social sharing functions
function sharePage(platform, url) {
    const title = encodeURIComponent(document.title);
    const encodedUrl = encodeURIComponent(url);

    const shareUrls = {
        facebook: `https://www.facebook.com/sharer/sharer.php?u=${encodedUrl}`,
        whatsapp: `https://api.whatsapp.com/send?text=${title}%20${encodedUrl}`
    };

    window.open(shareUrls[platform], '_blank');
}

// Copy URL to clipboard with visual feedback
function copyPageUrl(url) {
    navigator.clipboard.writeText(url).then(() => {
        notificationManager.showSuccess('تم نسخ الرابط بنجاح');
        const copyBtn = event.target.closest('.copy');
        if (copyBtn) {
            const originalHtml = copyBtn.innerHTML;
            copyBtn.innerHTML = '<i class="fas fa-check"></i> تم النسخ';
            setTimeout(() => {
                copyBtn.innerHTML = originalHtml;
            }, 2000);
        }
    }).catch(() => {
        notificationManager.showError('حدث خطأ أثناء نسخ الرابط');
    });
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    landingPagesManager.init();
});

// Social sharing functions
function sharePage(platform, url) {
    const title = encodeURIComponent(document.title);
    const encodedUrl = encodeURIComponent(url);

    const shareUrls = {
        facebook: `https://www.facebook.com/sharer/sharer.php?u=${encodedUrl}`,
        whatsapp: `https://api.whatsapp.com/send?text=${title}%20${encodedUrl}`
    };

    window.open(shareUrls[platform], '_blank');
}

// Copy URL to clipboard with visual feedback
function copyPageUrl(url) {
    navigator.clipboard.writeText(url).then(() => {
        notificationManager.showSuccess('تم نسخ الرابط بنجاح');
        const copyBtn = event.target.closest('.copy');
        if (copyBtn) {
            const originalHtml = copyBtn.innerHTML;
            copyBtn.innerHTML = '<i class="fas fa-check"></i> تم النسخ';
            setTimeout(() => {
                copyBtn.innerHTML = originalHtml;
            }, 2000);
        }
    }).catch(() => {
        notificationManager.showError('حدث خطأ أثناء نسخ الرابط');
    });
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    landingPagesManager.init();
});

// Social sharing functions
function sharePage(platform, url) {
    const title = encodeURIComponent(document.title);
    const encodedUrl = encodeURIComponent(url);

    const shareUrls = {
        facebook: `https://www.facebook.com/sharer/sharer.php?u=${encodedUrl}`,
        whatsapp: `https://api.whatsapp.com/send?text=${title}%20${encodedUrl}`
    };

    window.open(shareUrls[platform], '_blank');
}

// Copy URL to clipboard with visual feedback
function copyPageUrl(url) {
    navigator.clipboard.writeText(url).then(() => {
        notificationManager.showSuccess('تم نسخ الرابط بنجاح');
        const copyBtn = event.target.closest('.copy');
        if (copyBtn) {
            const originalHtml = copyBtn.innerHTML;
            copyBtn.innerHTML = '<i class="fas fa-check"></i> تم النسخ';
            setTimeout(() => {
                copyBtn.innerHTML = originalHtml;
            }, 2000);
        }
    }).catch(() => {
        notificationManager.showError('حدث خطأ أثناء نسخ الرابط');
    });
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    landingPagesManager.init();
});

// Social sharing functions
function sharePage(platform, url) {
    const title = encodeURIComponent(document.title);
    const encodedUrl = encodeURIComponent(url);

    const shareUrls = {
        facebook: `https://www.facebook.com/sharer/sharer.php?u=${encodedUrl}`,
        whatsapp: `https://api.whatsapp.com/send?text=${title}%20${encodedUrl}`
    };

    window.open(shareUrls[platform], '_blank');
}

// Copy URL to clipboard with visual feedback
function copyPageUrl(url) {
    navigator.clipboard.writeText(url).then(() => {
        notificationManager.showSuccess('تم نسخ الرابط بنجاح');
        const copyBtn = event.target.closest('.copy');
        if (copyBtn) {
            const originalHtml = copyBtn.innerHTML;
            copyBtn.innerHTML = '<i class="fas fa-check"></i> تم النسخ';
            setTimeout(() => {
                copyBtn.innerHTML = originalHtml;
            }, 2000);
        }
    }).catch(() => {
        notificationManager.showError('حدث خطأ أثناء نسخ الرابط');
    });
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    landingPagesManager.init();
});

// Social sharing functions
function sharePage(platform, url) {
    const title = encodeURIComponent(document.title);
    const encodedUrl = encodeURIComponent(url);

    const shareUrls = {
        facebook: `https://www.facebook.com/sharer/sharer.php?u=${encodedUrl}`,
        whatsapp: `https://api.whatsapp.com/send?text=${title}%20${encodedUrl}`
    };

    window.open(shareUrls[platform], '_blank');
}

// Copy URL to clipboard with visual feedback
function copyPageUrl(url) {
    navigator.clipboard.writeText(url).then(() => {
        notificationManager.showSuccess('تم نسخ الرابط بنجاح');
        const copyBtn = event.target.closest('.copy');
        if (copyBtn) {
            const originalHtml = copyBtn.innerHTML;
            copyBtn.innerHTML = '<i class="fas fa-check"></i> تم النسخ';
            setTimeout(() => {
                copyBtn.innerHTML = originalHtml;
            }, 2000);
        }
    }).catch(() => {
        notificationManager.showError('حدث خطأ أثناء نسخ الرابط');
    });
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    landingPagesManager.init();
});

// Social sharing functions
function sharePage(platform, url) {
    const title = encodeURIComponent(document.title);
    const encodedUrl = encodeURIComponent(url);

    const shareUrls = {
        facebook: `https://www.facebook.com/sharer/sharer.php?u=${encodedUrl}`,
        whatsapp: `https://api.whatsapp.com/send?text=${title}%20${encodedUrl}`
    };

    window.open(shareUrls[platform], '_blank');
}

// Copy URL to clipboard with visual feedback
function copyPageUrl(url) {
    navigator.clipboard.writeText(url).then(() => {
        notificationManager.showSuccess('تم نسخ الرابط بنجاح');
        const copyBtn = event.target.closest('.copy');
        if (copyBtn) {
            const originalHtml = copyBtn.innerHTML;
            copyBtn.innerHTML = '<i class="fas fa-check"></i> تم النسخ';
            setTimeout(() => {
                copyBtn.innerHTML = originalHtml;
            }, 2000);
        }
    }).catch(() => {
        notificationManager.showError('حدث خطأ أثناء نسخ الرابط');
    });
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    landingPagesManager.init();
});

// Social sharing functions
function sharePage(platform, url) {
    const title = encodeURIComponent(document.title);
    const encodedUrl = encodeURIComponent(url);

    const shareUrls = {
        facebook: `https://www.facebook.com/sharer/sharer.php?u=${encodedUrl}`,
        whatsapp: `https://api.whatsapp.com/send?text=${title}%20${encodedUrl}`
    };

    window.open(shareUrls[platform], '_blank');
}

// Copy URL to clipboard with visual feedback
function copyPageUrl(url) {
    navigator.clipboard.writeText(url).then(() => {
        notificationManager.showSuccess('تم نسخ الرابط بنجاح');
        const copyBtn = event.target.closest('.copy');
        if (copyBtn) {
            const originalHtml = copyBtn.innerHTML;
            copyBtn.innerHTML = '<i class="fas fa-check"></i> تم النسخ';
            setTimeout(() => {
                copyBtn.innerHTML = originalHtml;
            }, 2000);
        }
    }).catch(() => {
        notificationManager.showError('حدث خطأ أثناء نسخ الرابط');
    });
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    landingPagesManager.init();
});

// Social sharing functions
function sharePage(platform, url) {
    const title = encodeURIComponent(document.title);
    const encodedUrl = encodeURIComponent(url);

    const shareUrls = {
        facebook: `https://www.facebook.com/sharer/sharer.php?u=${encodedUrl}`,
        whatsapp: `https://api.whatsapp.com/send?text=${title}%20${encodedUrl}`
    };

    window.open(shareUrls[platform], '_blank');
}

// Copy URL to clipboard with visual feedback
function copyPageUrl(url) {
    navigator.clipboard.writeText(url).then(() => {
        notificationManager.showSuccess('تم نسخ الرابط بنجاح');
        const copyBtn = event.target.closest('.copy');
        if (copyBtn) {
            const originalHtml = copyBtn.innerHTML;
            copyBtn.innerHTML = '<i class="fas fa-check"></i> تم النسخ';
            setTimeout(() => {
                copyBtn.innerHTML = originalHtml;
            }, 2000);
        }
    }).catch(() => {
        notificationManager.showError('حدث خطأ أثناء نسخ الرابط');
    });
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    landingPagesManager.init();
});

// Social sharing functions
function sharePage(platform, url) {
    const title = encodeURIComponent(document.title);
    const encodedUrl = encodeURIComponent(url);

    const shareUrls = {
        facebook: `https://www.facebook.com/sharer/sharer.php?u=${encodedUrl}`,
        whatsapp: `https://api.whatsapp.com/send?text=${title}%20${encodedUrl}`
    };

    window.open(shareUrls[platform], '_blank');
}

// Copy URL to clipboard with visual feedback
function copyPageUrl(url) {
    navigator.clipboard.writeText(url).then(() => {
        notificationManager.showSuccess('تم نسخ الرابط بنجاح');
        const copyBtn = event.target.closest('.copy');
        if (copyBtn) {
            const originalHtml = copyBtn.innerHTML;
            copyBtn.innerHTML = '<i class="fas fa-check"></i> تم النسخ';
            setTimeout(() => {
                copyBtn.innerHTML = originalHtml;
            }, 2000);
        }
    }).catch(() => {
        notificationManager.showError('حدث خطأ أثناء نسخ الرابط');
    });
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    landingPagesManager.init();
});

// Social sharing functions
function sharePage(platform, url) {
    const title = encodeURIComponent(document.title);
    const encodedUrl = encodeURIComponent(url);

    const shareUrls = {
        facebook: `https://www.facebook.com/sharer/sharer.php?u=${encodedUrl}`,
        whatsapp: `https://api.whatsapp.com/send?text=${title}%20${encodedUrl}`
    };

    window.open(shareUrls[platform], '_blank');
}

// Copy URL to clipboard with visual feedback
function copyPageUrl(url) {
    navigator.clipboard.writeText(url).then(() => {
        notificationManager.showSuccess('تم نسخ الرابط بنجاح');
        const copyBtn = event.target.closest('.copy');
        if (copyBtn) {
            const originalHtml = copyBtn.innerHTML;
            copyBtn.innerHTML = '<i class="fas fa-check"></i> تم النسخ';
            setTimeout(() => {
                copyBtn.innerHTML = originalHtml;
            }, 2000);
        }
    }).catch(() => {
        notificationManager.showError('حدث خطأ أثناء نسخ الرابط');
    });
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    landingPagesManager.init();
});

// Social sharing functions
function sharePage(platform, url) {
    const title = encodeURIComponent(document.title);
    const encodedUrl = encodeURIComponent(url);

    const shareUrls = {
        facebook: `https://www.facebook.com/sharer/sharer.php?u=${encodedUrl}`,
        whatsapp: `https://api.whatsapp.com/send?text=${title}%20${encodedUrl}`
    };

    window.open(shareUrls[platform], '_blank');
}

// Copy URL to clipboard with visual feedback
function copyPageUrl(url) {
    navigator.clipboard.writeText(url).then(() => {
        notificationManager.showSuccess('تم نسخ الرابط بنجاح');
        const copyBtn = event.target.closest('.copy');
        if (copyBtn) {
            const originalHtml = copyBtn.innerHTML;
            copyBtn.innerHTML = '<i class="fas fa-check"></i> تم النسخ';
            setTimeout(() => {
                copyBtn.innerHTML = originalHtml;
            }, 2000);
        }
    }).catch(() => {
        notificationManager.showError('حدث خطأ أثناء نسخ الرابط');
    });
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    landingPagesManager.init();
});

// Social sharing functions
function sharePage(platform, url) {
    const title = encodeURIComponent(document.title);
    const encodedUrl = encodeURIComponent(url);

    const shareUrls = {
        facebook: `https://www.facebook.com/sharer/sharer.php?u=${encodedUrl}`,
        whatsapp: `https://api.whatsapp.com/send?text=${title}%20${encodedUrl}`
    };

    window.open(shareUrls[platform], '_blank');
}

// Copy URL to clipboard with visual feedback
function copyPageUrl(url) {
    navigator.clipboard.writeText(url).then(() => {
        notificationManager.showSuccess('تم نسخ الرابط بنجاح');
        const copyBtn = event.target.closest('.copy');
        if (copyBtn) {
            const originalHtml = copyBtn.innerHTML;
            copyBtn.innerHTML = '<i class="fas fa-check"></i> تم النسخ';
            setTimeout(() => {
                copyBtn.innerHTML = originalHtml;
            }, 2000);
        }
    }).catch(() => {
        notificationManager.showError('حدث خطأ أثناء نسخ الرابط');
    });
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    landingPagesManager.init();
});

// Social sharing functions
function sharePage(platform, url) {
    const title = encodeURIComponent(document.title);
    const encodedUrl = encodeURIComponent(url);

    const shareUrls = {
        facebook: `https://www.facebook.com/sharer/sharer.php?u=${encodedUrl}`,
        whatsapp: `https://api.whatsapp.com/send?text=${title}%20${encodedUrl}`
    };

    window.open(shareUrls[platform], '_blank');
}

// Copy URL to clipboard with visual feedback
function copyPageUrl(url) {
    navigator.clipboard.writeText(url).then(() => {
        notificationManager.showSuccess('تم نسخ الرابط بنجاح');
        const copyBtn = event.target.closest('.copy');
        if (copyBtn) {
            const originalHtml = copyBtn.innerHTML;
            copyBtn.innerHTML = '<i class="fas fa-check"></i> تم النسخ';
            setTimeout(() => {
                copyBtn.innerHTML = originalHtml;
            }, 2000);
        }
    }).catch(() => {
        notificationManager.showError('حدث خطأ أثناء نسخ الرابط');
    });
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    landingPagesManager.init();
});

// Social sharing functions
function sharePage(platform, url) {
    const title = encodeURIComponent(document.title);
    const encodedUrl = encodeURIComponent(url);

    const shareUrls = {
        facebook: `https://www.facebook.com/sharer/sharer.php?u=${encodedUrl}`,
        whatsapp: `https://api.whatsapp.com/send?text=${title}%20${encodedUrl}`
    };

    window.open(shareUrls[platform], '_blank');
}

// Copy URL to clipboard with visual feedback
function copyPageUrl(url) {
    navigator.clipboard.writeText(url).then(() => {
        notificationManager.showSuccess('تم نسخ الرابط بنجاح');
        const copyBtn = event.target.closest('.copy');
        if (copyBtn) {
            const originalHtml = copyBtn.innerHTML;
            copyBtn.innerHTML = '<i class="fas fa-check"></i> تم النسخ';
            setTimeout(() => {
                copyBtn.innerHTML = originalHtml;
            }, 2000);
        }
    }).catch(() => {
        notificationManager.showError('حدث خطأ أثناء نسخ الرابط');
    });
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    landingPagesManager.init();
});

// Social sharing functions
function sharePage(platform, url) {
    const title = encodeURIComponent(document.title);
    const encodedUrl = encodeURIComponent(url);

    const shareUrls = {
        facebook: `https://www.facebook.com/sharer/sharer.php?u=${encodedUrl}`,
        whatsapp: `https://api.whatsapp.com/send?text=${title}%20${encodedUrl}`
    };

    window.open(shareUrls[platform], '_blank');
}

// Copy URL to clipboard with visual feedback
function copyPageUrl(url) {
    navigator.clipboard.writeText(url).then(() => {
        notificationManager.showSuccess('تم نسخ الرابط بنجاح');
        const copyBtn = event.target.closest('.copy');
        if (copyBtn) {
            const originalHtml = copyBtn.innerHTML;
            copyBtn.innerHTML = '<i class="fas fa-check"></i> تم النسخ';
            setTimeout(() => {
                copyBtn.innerHTML = originalHtml;
            }, 2000);
        }
    }).catch(() => {
        notificationManager.showError('فشل في نسخ الرابط');
    });
}
