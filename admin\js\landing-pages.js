// Landing Pages Manager - Clean Implementation
// Use the existing notificationManager from admin.js

// Utility function for safe API calls with proper error handling
async function safeApiCall(url, options = {}) {
    try {
        const response = await fetch(url, options);

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const text = await response.text();

        // Check if response is empty
        if (!text.trim()) {
            console.warn('Empty response from API:', url);
            return [];
        }

        // Try to parse JSON
        try {
            return JSON.parse(text);
        } catch (parseError) {
            console.error('JSON parse error for response:', text);
            throw new Error('Invalid JSON response from server');
        }
    } catch (error) {
        console.error('API call failed:', error);
        throw error;
    }
}

// Landing Pages Manager - Single Instance
const landingPagesManager = {
    initialized: false,
    modal: null,
    form: null,
    productSelect: null,
    imagePreview: null,
    addButton: null,

    init() {
        // Prevent multiple initializations
        if (this.initialized) {
            console.log('Landing Pages Manager already initialized');
            return;
        }

        console.log('🚀 Initializing Landing Pages Manager...');

        // Check if elements exist before proceeding
        this.modal = document.getElementById('landingPageModal');
        this.form = document.getElementById('landingPageForm');
        this.productSelect = document.getElementById('productSelect');
        this.imagePreview = document.getElementById('imagePreview');
        this.addButton = document.getElementById('addLandingPageBtn');

        console.log('🔍 Checking for required DOM elements:', {
            modal: !!this.modal,
            form: !!this.form,
            productSelect: !!this.productSelect,
            imagePreview: !!this.imagePreview,
            addButton: !!this.addButton
        });

        if (!this.modal) {
            console.error('❌ Modal element #landingPageModal not found');
        }
        if (!this.form) {
            console.error('❌ Form element #landingPageForm not found');
        }
        if (!this.productSelect) {
            console.error('❌ Select element #productSelect not found');
        }
        if (!this.addButton) {
            console.error('❌ Button element #addLandingPageBtn not found');
        }

        if (!this.modal || !this.form || !this.productSelect || !this.addButton) {
            console.error('❌ Required elements not found for landing pages manager - initialization failed');
            return;
        }

        console.log('✅ All required elements found, proceeding with initialization...');

        this.bindEvents();
        this.loadActiveProducts(); // Load only active products for selection

        // Force show landing pages section for testing
        this.ensureSectionVisible();

        this.loadLandingPages();

        // Mark as initialized
        this.initialized = true;
        console.log('🎉 Landing Pages Manager initialized successfully');

        // Add a test to verify button works
        this.testButtonFunctionality();
    },

    bindEvents() {
        console.log('🔗 Binding events...');

        // Add button click handler
        if (this.addButton) {
            console.log('✅ Binding click event to add button:', this.addButton);
            this.addButton.addEventListener('click', (e) => {
                e.preventDefault();
                console.log('🖱️ Add landing page button clicked!');
                console.log('Button element:', e.target);
                console.log('Opening modal...');
                this.openModal();
            });
            console.log('✅ Add button event bound successfully');
        } else {
            console.error('❌ Add button not found - cannot bind click event');
        }

        // Form submit handler
        if (this.form) {
            this.form.addEventListener('submit', (e) => this.handleSubmit(e));
        }

        // Image upload handler
        const imageInput = document.getElementById('landingPageImages');
        if (imageInput) {
            imageInput.addEventListener('change', (e) => this.handleImageUpload(e));
        }

        // Close button handler
        const closeButton = this.modal ? this.modal.querySelector('.cancel-button') : null;
        if (closeButton) {
            closeButton.addEventListener('click', (e) => {
                e.preventDefault();
                this.closeModal();
            });
        }

        // Product selection change handler
        if (this.productSelect) {
            this.productSelect.addEventListener('change', (e) => {
                this.handleProductSelection(e.target.value);
            });
        }

        // Close modal when clicking outside
        if (this.modal) {
            window.addEventListener('click', (event) => {
                if (event.target === this.modal) {
                    this.closeModal();
                }
            });
        }
    },

    async loadActiveProducts() {
        try {
            console.log('📦 Loading active products for landing page selection...');
            const response = await safeApiCall('../php/api/products.php');

            // Handle both array and object responses
            let products = [];
            if (Array.isArray(response)) {
                products = response;
            } else if (response.success && Array.isArray(response.products)) {
                products = response.products;
            } else if (response.products) {
                products = response.products;
            }

            this.productSelect.innerHTML = '<option value="">اختر منتجاً</option>';

            // Filter only active products
            const activeProducts = products.filter(product => product.actif == 1 || product.actif === true);

            console.log(`Found ${activeProducts.length} active products out of ${products.length} total products`);

            if (activeProducts.length === 0) {
                const option = document.createElement('option');
                option.value = '';
                option.textContent = 'لا توجد منتجات مفعلة';
                option.disabled = true;
                this.productSelect.appendChild(option);
                return;
            }

            activeProducts.forEach(product => {
                const option = document.createElement('option');
                option.value = product.id;
                option.textContent = `${product.titre || product.title || `Product ${product.id}`} (${this.getProductTypeText(product.type)})`;
                option.setAttribute('data-product-type', product.type);
                this.productSelect.appendChild(option);
            });

            console.log('Active products loaded successfully');
        } catch (error) {
            console.error('Error loading active products:', error);
            this.productSelect.innerHTML = '<option value="">خطأ في تحميل المنتجات</option>';
            notificationManager.showError('حدث خطأ أثناء تحميل المنتجات المفعلة');
        }
    },

    // Helper function to get product type text in Arabic
    getProductTypeText(type) {
        const types = {
            'book': 'كتاب',
            'laptop': 'حاسوب محمول',
            'bag': 'حقيبة'
        };
        return types[type] || type;
    },

    // Function to refresh product selection (called when products are activated/deactivated)
    refreshProductSelect() {
        console.log('Refreshing product selection...');
        this.loadActiveProducts();
    },

    // Ensure landing pages section is visible
    ensureSectionVisible() {
        console.log('👁️ Ensuring landing pages section is visible...');
        const section = document.getElementById('landingPages');
        if (section) {
            const currentDisplay = window.getComputedStyle(section).display;
            const hasActiveClass = section.classList.contains('active');
            console.log('👁️ Current section display:', currentDisplay);
            console.log('👁️ Has active class:', hasActiveClass);

            if (currentDisplay === 'none' || !hasActiveClass) {
                console.log('👁️ Making section visible by adding active class...');

                // Remove active class from all sections first
                document.querySelectorAll('.content-section').forEach(s => {
                    s.classList.remove('active');
                });

                // Add active class to landing pages section
                section.classList.add('active');

                // Also update navigation
                document.querySelectorAll('.admin-nav ul li').forEach(navItem => {
                    navItem.classList.remove('active');
                });
                const navItem = document.querySelector('.admin-nav ul li[data-section="landingPages"]');
                if (navItem) {
                    navItem.classList.add('active');
                    console.log('👁️ Navigation item activated');
                }
            }

            // Also check if container exists
            const container = document.getElementById('landingPagesContainer');
            console.log('👁️ Container exists:', !!container);
            if (container) {
                console.log('👁️ Container display:', window.getComputedStyle(container).display);
            }
        } else {
            console.error('❌ Landing pages section not found!');
        }
    },

    // Test button functionality
    testButtonFunctionality() {
        console.log('🧪 Testing button functionality...');

        if (this.addButton) {
            console.log('✅ Add button found:', this.addButton);
            console.log('Button text:', this.addButton.textContent);
            console.log('Button ID:', this.addButton.id);
            console.log('Button classes:', this.addButton.className);

            // Test if button is clickable
            const rect = this.addButton.getBoundingClientRect();
            console.log('Button position:', rect);
            console.log('Button visible:', rect.width > 0 && rect.height > 0);

            // Check if button's parent section is visible
            const section = this.addButton.closest('.content-section');
            if (section) {
                const sectionRect = section.getBoundingClientRect();
                console.log('Section position:', sectionRect);
                console.log('Section visible:', sectionRect.width > 0 && sectionRect.height > 0);
                console.log('Section display:', window.getComputedStyle(section).display);
            }

            // Add a temporary test click handler
            const testHandler = () => {
                console.log('🎉 TEST: Button click detected!');
            };

            this.addButton.addEventListener('click', testHandler, { once: true });
            console.log('✅ Test click handler added');
        } else {
            console.error('❌ Add button not found during test');
        }
    },

    // Handle product selection change
    handleProductSelection(productId) {
        const statusDiv = document.getElementById('productSelectionStatus');
        const titleInput = document.getElementById('landingPageTitle');

        if (!statusDiv) return;

        if (!productId) {
            statusDiv.style.display = 'none';
            if (titleInput) titleInput.value = '';
            return;
        }

        // Find the selected product option
        const selectedOption = this.productSelect.querySelector(`option[value="${productId}"]`);
        if (selectedOption) {
            const productName = selectedOption.textContent;
            const productType = selectedOption.getAttribute('data-product-type');

            statusDiv.className = 'product-selection-status active';
            statusDiv.innerHTML = `
                <i class="fas fa-check-circle"></i>
                تم اختيار: ${productName}
            `;

            // Auto-fill the landing page title
            if (titleInput && !titleInput.value) {
                const cleanProductName = productName.split(' (')[0]; // Remove type info
                titleInput.value = `صفحة هبوط - ${cleanProductName}`;
            }
        }
    },

    async loadLandingPages() {
        console.log('🔄 Loading landing pages...');
        try {
            console.log('📡 Making API call to: ../php/api/landing-pages.php');
            const response = await safeApiCall('../php/api/landing-pages.php');
            console.log('📡 Raw API Response:', response);
            console.log('📡 Response type:', typeof response);
            console.log('📡 Response keys:', Object.keys(response || {}));

            const pages = response.data || response; // Handle both formats
            console.log('📋 Landing pages data:', pages);
            console.log('📋 Pages type:', typeof pages);
            console.log('📋 Is array:', Array.isArray(pages));
            console.log('📋 Pages length:', pages ? pages.length : 'undefined');

            this.displayLandingPages(pages);
        } catch (error) {
            console.error('❌ Error loading landing pages:', error);
            console.error('❌ Error stack:', error.stack);
            notificationManager.showError('حدث خطأ أثناء تحميل صفحات الهبوط: ' + error.message);
        }
    },

    displayLandingPages(pages) {
        console.log('🎨 Displaying landing pages:', pages);
        console.log('🎨 Pages parameter type:', typeof pages);
        console.log('🎨 Pages parameter value:', pages);

        const container = document.getElementById('landingPagesContainer');
        console.log('🎨 Container found:', !!container);
        console.log('🎨 Container element:', container);

        if (!container) {
            console.error('❌ Landing pages container not found!');
            return;
        }

        if (!Array.isArray(pages) || pages.length === 0) {
            console.log('📭 No landing pages to display');
            console.log('📭 Pages is array:', Array.isArray(pages));
            console.log('📭 Pages length:', pages ? pages.length : 'undefined');
            container.innerHTML = '<p class="no-data" style="text-align: center; padding: 40px; color: #666;">لا توجد صفحات هبوط</p>';
            return;
        }

        console.log(`📄 Displaying ${pages.length} landing pages`);

        container.innerHTML = pages.map(page => `
            <div class="landing-page-card" style="background: white; border-radius: 10px; padding: 20px; margin-bottom: 20px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
                <div style="display: flex; justify-content: space-between; align-items: start; margin-bottom: 15px;">
                    <div>
                        <h3 style="margin: 0 0 10px 0; color: #333;">${page.titre || 'صفحة هبوط'}</h3>
                        <p style="margin: 0; color: #666;">📦 المنتج: ${page.product_title || 'غير محدد'}</p>
                        <p style="margin: 5px 0 0 0; color: #888; font-size: 0.9em;">🔗 ${page.lien_url || ''}</p>
                    </div>
                    <div style="display: flex; gap: 10px;">
                        ${page.images && page.images.length > 0 ? `<span style="background: #e8f4fd; color: #667eea; padding: 4px 8px; border-radius: 12px; font-size: 0.8em;">📸 ${page.images.length} صور</span>` : ''}
                    </div>
                </div>
                <div class="page-actions" style="display: flex; gap: 10px; flex-wrap: wrap;">
                    <a href="${page.lien_url || '#'}" target="_blank" class="view-btn" style="background: #28a745; color: white; padding: 8px 15px; border-radius: 5px; text-decoration: none; font-size: 0.9em;">
                        <i class="fas fa-eye"></i> عرض
                    </a>
                    <button onclick="landingPagesManager.clonePage(${page.id})" class="clone-btn" style="background: #17a2b8; color: white; border: none; padding: 8px 15px; border-radius: 5px; cursor: pointer; font-size: 0.9em;">
                        <i class="fas fa-copy"></i> نسخ
                    </button>
                    <button onclick="landingPagesManager.editPage(${page.id})" class="edit-btn" style="background: #ffc107; color: #333; border: none; padding: 8px 15px; border-radius: 5px; cursor: pointer; font-size: 0.9em;">
                        <i class="fas fa-edit"></i> تعديل
                    </button>
                    <button onclick="landingPagesManager.deletePage(${page.id})" class="delete-btn" style="background: #dc3545; color: white; border: none; padding: 8px 15px; border-radius: 5px; cursor: pointer; font-size: 0.9em;">
                        <i class="fas fa-trash"></i> حذف
                    </button>
                    <button onclick="copyPageUrl('${page.lien_url || ''}', this)" class="copy-btn" style="background: #6c757d; color: white; border: none; padding: 8px 15px; border-radius: 5px; cursor: pointer; font-size: 0.9em;">
                        <i class="fas fa-link"></i> نسخ الرابط
                    </button>
                </div>
            </div>
        `).join('');
    },

    openModal() {
        console.log('🚀 Opening modal...');

        if (!this.modal) {
            console.error('❌ Modal element not found');
            return;
        }

        // Ensure the landing pages section is visible first
        const landingPagesSection = document.getElementById('landingPages');
        if (landingPagesSection) {
            const sectionDisplay = window.getComputedStyle(landingPagesSection).display;
            console.log('Landing pages section display:', sectionDisplay);

            if (sectionDisplay === 'none') {
                console.log('⚠️ Section not visible, making it visible first...');
                landingPagesSection.style.display = 'block';
            }
        }

        // Force modal display with important styles
        this.modal.style.setProperty('display', 'block', 'important');
        this.modal.style.setProperty('opacity', '1', 'important');
        this.modal.style.setProperty('visibility', 'visible', 'important');
        this.modal.style.setProperty('z-index', '9999', 'important');
        this.modal.style.setProperty('position', 'fixed', 'important');
        this.modal.style.setProperty('top', '0', 'important');
        this.modal.style.setProperty('left', '0', 'important');
        this.modal.style.setProperty('width', '100%', 'important');
        this.modal.style.setProperty('height', '100%', 'important');

        // Add a class for CSS targeting
        this.modal.classList.add('modal-open');

        // Reset form
        if (this.form) {
            this.form.reset();
        }

        // Clear image preview
        if (this.imagePreview) {
            this.imagePreview.innerHTML = '';
        }

        // Refresh active products list to ensure it's up to date
        this.refreshProductSelect();

        // Initialize TinyMCE editors for the modal with delay
        setTimeout(() => {
            this.initModalTinyMCE();
        }, 100);

        console.log('✅ Modal opened successfully');

        // Debug: log modal styles
        console.log('Modal styles:', {
            display: this.modal.style.display,
            opacity: this.modal.style.opacity,
            visibility: this.modal.style.visibility,
            zIndex: this.modal.style.zIndex,
            position: this.modal.style.position
        });
    },

    closeModal() {
        console.log('🚪 Closing modal...');

        if (!this.modal) {
            console.error('❌ Modal not found for closing');
            return;
        }

        // Hide modal
        this.modal.style.setProperty('display', 'none', 'important');
        this.modal.style.setProperty('opacity', '0', 'important');
        this.modal.style.setProperty('visibility', 'hidden', 'important');

        // Clean up TinyMCE instances
        this.cleanupTinyMCE();

        // Remove modal-open class
        this.modal.classList.remove('modal-open');

        // Reset form
        if (this.form) {
            this.form.reset();
        }

        // Clear image preview
        if (this.imagePreview) {
            this.imagePreview.innerHTML = '';
        }

        console.log('✅ Modal closed successfully');
    },

    initModalTinyMCE() {
        console.log('Initializing TinyMCE for landing pages modal');

        // Clean up existing instances first
        this.cleanupTinyMCE();

        // Wait a bit for DOM to be ready
        setTimeout(() => {
            const rightContent = document.getElementById('rightContent');
            const leftContent = document.getElementById('leftContent');

            if (!rightContent || !leftContent) {
                console.error('TinyMCE target elements not found');
                return;
            }

            tinymce.init({
                selector: '#rightContent, #leftContent',
                // No license_key needed for self-hosted version
                directionality: 'rtl',
                language: 'ar',
                height: 300,
                readonly: false,
                plugins: 'lists link image media table code wordcount',
                toolbar: 'undo redo | formatselect | bold italic | alignright aligncenter alignleft | bullist numlist | link image media | table | code',
                content_css: [
                    'https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@400;500;600;700&display=swap'
                ],
                content_style: 'body { font-family: "Noto Sans Arabic", sans-serif; direction: rtl; text-align: right; }',
                promotion: false,
                branding: false,
                menubar: false,
                setup: function(editor) {
                    editor.on('init', function() {
                        console.log('TinyMCE initialized for:', editor.id);
                        editor.getContainer().style.direction = 'rtl';
                        // Force editable mode
                        editor.mode.set('design');
                        editor.getBody().removeAttribute('readonly');
                        editor.getBody().contentEditable = true;
                    });
                },
                init_instance_callback: function(editor) {
                    console.log('TinyMCE ready for:', editor.id);
                    // Ensure editor is fully editable
                    try {
                        // setMode is deprecated in newer versions, use mode.set instead
                        if (editor.mode && editor.mode.set) {
                            editor.mode.set('design');
                        }
                        editor.getBody().removeAttribute('readonly');
                        editor.getBody().contentEditable = true;
                    } catch (error) {
                        console.warn('Could not set editor mode:', error);
                    }
                }
            }).catch(function(error) {
                console.error('TinyMCE initialization failed for landing pages:', error);
            });
        }, 200);
    },

    cleanupTinyMCE() {
        if (typeof tinymce !== 'undefined') {
            try {
                const rightEditor = tinymce.get('rightContent');
                const leftEditor = tinymce.get('leftContent');

                if (rightEditor) {
                    rightEditor.remove();
                }
                if (leftEditor) {
                    leftEditor.remove();
                }
            } catch (error) {
                console.warn('Error cleaning up TinyMCE:', error);
            }
        }
    },

    handleImageUpload(event) {
        const files = event.target.files;
        this.imagePreview.innerHTML = '';

        Array.from(files).forEach(file => {
            const reader = new FileReader();
            reader.onload = (e) => {
                const preview = document.createElement('div');
                preview.className = 'preview-image';
                preview.style.backgroundImage = `url(${e.target.result})`;

                const removeBtn = document.createElement('button');
                removeBtn.className = 'remove-image';
                removeBtn.innerHTML = '<i class="fas fa-times"></i>';
                removeBtn.onclick = () => preview.remove();

                preview.appendChild(removeBtn);
                this.imagePreview.appendChild(preview);
            };
            reader.readAsDataURL(file);
        });
    },

    async handleSubmit(event) {
        event.preventDefault();

        const formData = new FormData(this.form);

        // Get TinyMCE content safely
        try {
            const rightEditor = tinymce.get('rightContent');
            const leftEditor = tinymce.get('leftContent');

            if (rightEditor) {
                formData.append('rightContent', rightEditor.getContent());
            }
            if (leftEditor) {
                formData.append('leftContent', leftEditor.getContent());
            }
        } catch (error) {
            console.warn('Error getting TinyMCE content:', error);
        }

        // Add URL images to FormData
        const urlInputs = document.querySelectorAll('input[name="imageUrls[]"]');
        urlInputs.forEach((input, index) => {
            if (input.value.trim()) {
                formData.append(`imageUrls[${index}]`, input.value.trim());
            }
        });

        try {
            console.log('📤 Sending form data to API...');
            const response = await fetch('../php/api/landing-pages.php', {
                method: 'POST',
                body: formData
            });

            console.log('📥 Response status:', response.status);

            if (!response.ok) {
                const errorText = await response.text();
                console.error('❌ HTTP Error:', response.status, errorText);
                throw new Error(`HTTP ${response.status}: ${errorText.substring(0, 200)}`);
            }

            const responseText = await response.text();
            console.log('📥 Raw response:', responseText);

            let result;
            try {
                result = JSON.parse(responseText);
            } catch (parseError) {
                console.error('❌ JSON Parse Error:', parseError);
                console.error('❌ Response was:', responseText);
                throw new Error('Réponse invalide du serveur (pas du JSON)');
            }

            if (result.success) {
                console.log('✅ Landing page created successfully');
                notificationManager.showSuccess('تم إنشاء صفحة الهبوط بنجاح');
                this.closeModal();
                this.loadLandingPages();
            } else {
                notificationManager.showError(result.message || 'حدث خطأ أثناء إنشاء صفحة الهبوط');
            }
        } catch (error) {
            console.error('❌ Error submitting form:', error);
            notificationManager.showError(`حدث خطأ أثناء إنشاء صفحة الهبوط: ${error.message}`);
        }
    },

    async editPage(id) {
        // Implementation for editing landing page
        console.log('Edit page:', id);
    },

    async deletePage(id) {
        if (confirm('هل أنت متأكد من حذف صفحة الهبوط هذه؟')) {
            try {
                const response = await fetch(`../php/api/landing-pages.php?id=${id}`, {
                    method: 'DELETE'
                });
                const result = await response.json();

                if (result.success) {
                    notificationManager.showSuccess('تم حذف صفحة الهبوط بنجاح');
                    this.loadLandingPages();
                } else {
                    notificationManager.showError(result.message || 'حدث خطأ أثناء حذف صفحة الهبوط');
                }
            } catch (error) {
                console.error('Error deleting landing page:', error);
                notificationManager.showError('حدث خطأ أثناء حذف صفحة الهبوط');
            }
        }
    },

    async clonePage(pageId) {
        if (!confirm('هل تريد إنشاء نسخة من هذه الصفحة؟')) {
            return;
        }

        try {
            const response = await fetch(`../php/api/landing-pages.php?action=clone&id=${pageId}`, {
                method: 'POST'
            });
            const result = await response.json();

            if (result.success) {
                notificationManager.showSuccess('تم إنشاء نسخة من الصفحة بنجاح');
                this.loadLandingPages();
            } else {
                notificationManager.showError(result.message || 'حدث خطأ أثناء نسخ الصفحة');
            }
        } catch (error) {
            console.error('Error cloning landing page:', error);
            notificationManager.showError('حدث خطأ أثناء نسخ الصفحة');
        }
    }
};

// Social sharing functions
function sharePage(platform, url) {
    const title = encodeURIComponent(document.title);
    const encodedUrl = encodeURIComponent(url);

    const shareUrls = {
        facebook: `https://www.facebook.com/sharer/sharer.php?u=${encodedUrl}`,
        twitter: `https://twitter.com/intent/tweet?url=${encodedUrl}`,
        whatsapp: `https://api.whatsapp.com/send?text=${title}%20${encodedUrl}`,
        telegram: `https://t.me/share/url?url=${encodedUrl}`
    };

    if (shareUrls[platform]) {
        window.open(shareUrls[platform], '_blank', 'width=600,height=400');
    }
}

// Copy URL to clipboard with visual feedback
function copyPageUrl(url, buttonElement) {
    navigator.clipboard.writeText(url).then(() => {
        notificationManager.showSuccess('تم نسخ الرابط بنجاح');
        if (buttonElement) {
            const originalHtml = buttonElement.innerHTML;
            buttonElement.innerHTML = '<i class="fas fa-check"></i> تم النسخ';
            setTimeout(() => {
                buttonElement.innerHTML = originalHtml;
            }, 2000);
        }
    }).catch(() => {
        notificationManager.showError('فشل في نسخ الرابط');
    });
}

// Function to add URL image input
function addUrlImageInput() {
    const container = document.getElementById('urlImagesContainer');
    const newInput = document.createElement('div');
    newInput.className = 'url-image-input';
    newInput.style.cssText = 'display: flex; align-items: center; margin-bottom: 10px;';
    newInput.innerHTML = `
        <input
            type="url"
            name="imageUrls[]"
            placeholder="https://images.unsplash.com/photo-example.jpg"
            style="flex: 1; padding: 8px; border: 1px solid #ddd; border-radius: 4px; margin-left: 10px;"
        />
        <button
            type="button"
            onclick="removeUrlImageInput(this)"
            class="remove-btn"
            style="background: #dc3545; color: white; border: none; padding: 8px 12px; border-radius: 4px; cursor: pointer; margin-left: 5px;"
            title="حذف هذه الصورة"
        >
            ❌
        </button>
        <button
            type="button"
            onclick="addUrlImageInput()"
            class="add-btn"
            style="background: #28a745; color: white; border: none; padding: 8px 12px; border-radius: 4px; cursor: pointer;"
            title="إضافة صورة أخرى"
        >
            ➕
        </button>
    `;
    container.appendChild(newInput);
}

function removeUrlImageInput(button) {
    const container = document.getElementById('urlImagesContainer');
    const inputs = container.querySelectorAll('.url-image-input');

    // Keep at least one input
    if (inputs.length > 1) {
        button.parentElement.remove();
    } else {
        // Clear the input instead of removing it
        const input = button.parentElement.querySelector('input');
        input.value = '';
    }
}

// Make landingPagesManager globally accessible
window.landingPagesManager = landingPagesManager;

// Single initialization - prevent duplicates
if (!window.landingPagesInitialized) {
    window.landingPagesInitialized = true;

    // Initialize when DOM is loaded or immediately if already loaded
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', () => {
            landingPagesManager.init();
        });
    } else {
        landingPagesManager.init();
    }
}
