// Product management functionality
const productManager = {
    init() {
        this.bindEvents();
        this.loadProducts();
    },

    bindEvents() {
        // Add product button
        document.querySelector('#addProduct').addEventListener('click', () => {
            document.querySelector('#productModal').style.display = 'flex';
        });

        // Close modal button
        document.querySelector('.close-modal').addEventListener('click', () => {
            document.querySelector('#productModal').style.display = 'none';
        });

        // Product form submission
        document.querySelector('#productForm').addEventListener('submit', (e) => {
            e.preventDefault();
            this.handleProductSubmit(e.target);
        });

        // Product type selection change
        document.querySelector('#productType').addEventListener('change', (e) => {
            this.toggleSpecificFields(e.target.value);
        });
    },

    toggleSpecificFields(productType) {
        // Hide all specific fields first
        document.querySelectorAll('.specific-fields').forEach(field => {
            field.style.display = 'none';
        });

        // Show fields specific to the selected product type
        const specificField = document.querySelector(`#${productType}Fields`);
        if (specificField) {
            specificField.style.display = 'block';
        }
    },

    async loadProducts() {
        try {
            const response = await fetch('/php/api/products.php');
            const data = await response.json();

            if (data.success) {
                this.displayProducts(data.products);
            } else {
                throw new Error(data.message);
            }
        } catch (error) {
            console.error('Error loading products:', error);
            // Show error message to user
            this.showNotification('error', 'Failed to load products');
        }
    },

    displayProducts(products) {
        const tbody = document.querySelector('#productList tbody');
        tbody.innerHTML = '';

        products.forEach(product => {
            const row = document.createElement('tr');
            
            // Create table cells
            row.innerHTML = `
                <td><img src="${product.image_url}" alt="${product.titre}" class="product-thumbnail"></td>
                <td>${product.titre}</td>
                <td>${product.prix} دج</td>
                <td>${product.stock}</td>
                <td class="actions">
                    ${this.createActionButtons(product)}
                </td>
            `;

            tbody.appendChild(row);

            // Add event listeners for action buttons
            this.bindActionButtons(row, product);
        });
    },

    createActionButtons(product) {
        const hasLandingPage = product.has_landing_page;
        const landingPageButton = hasLandingPage 
            ? `<button class="view-landing" data-url="${product.landing_url}">إقرأ أكثر...</button>`
            : `<button class="create-landing">إنشاء صفحة هبوط</button>`;

        return `
            <button class="edit-product" title="تعديل"><i class="fas fa-edit"></i></button>
            <button class="delete-product" title="حذف"><i class="fas fa-trash"></i></button>
            ${landingPageButton}
            <div class="share-buttons">
                <button class="share-facebook" data-url="${product.url}" title="مشاركة على فيسبوك">
                    <i class="fab fa-facebook-f"></i>
                </button>
                <button class="share-whatsapp" data-url="${product.url}" data-text="${product.titre}" title="مشاركة على واتساب">
                    <i class="fab fa-whatsapp"></i>
                </button>
                <button class="copy-link" data-url="${product.url}" title="نسخ الرابط">
                    <i class="fas fa-link"></i>
                </button>
            </div>
        `;
    },

    bindActionButtons(row, product) {
        // Edit button
        row.querySelector('.edit-product').addEventListener('click', () => {
            this.editProduct(product);
        });

        // Delete button
        row.querySelector('.delete-product').addEventListener('click', () => {
            this.deleteProduct(product.id);
        });

        // Landing page buttons
        const viewLanding = row.querySelector('.view-landing');
        const createLanding = row.querySelector('.create-landing');

        if (viewLanding) {
            viewLanding.addEventListener('click', () => {
                window.open(viewLanding.dataset.url, '_blank');
            });
        }

        if (createLanding) {
            createLanding.addEventListener('click', () => {
                landingPageManager.openModal(product);
            });
        }

        // Share buttons
        const shareButtons = row.querySelector('.share-buttons');
        if (shareButtons) {
            // Facebook share
            shareButtons.querySelector('.share-facebook').addEventListener('click', (e) => {
                e.preventDefault();
                const url = e.target.closest('.share-facebook').dataset.url;
                shareOnFacebook(url);
            });

            // WhatsApp share
            shareButtons.querySelector('.share-whatsapp').addEventListener('click', (e) => {
                e.preventDefault();
                const button = e.target.closest('.share-whatsapp');
                shareOnWhatsApp(button.dataset.text, button.dataset.url);
            });

            // Copy link
            shareButtons.querySelector('.copy-link').addEventListener('click', (e) => {
                e.preventDefault();
                const url = e.target.closest('.copy-link').dataset.url;
                copyToClipboard(url);
            });
        }
    },

    async handleProductSubmit(form) {
        const formData = new FormData(form);

        try {
            const response = await fetch('/php/api/products.php', {
                method: 'POST',
                body: formData
            });

            const data = await response.json();

            if (data.success) {
                this.showNotification('success', 'Product saved successfully');
                document.querySelector('#productModal').style.display = 'none';
                this.loadProducts(); // Refresh product list
            } else {
                throw new Error(data.message);
            }
        } catch (error) {
            console.error('Error saving product:', error);
            this.showNotification('error', 'Failed to save product');
        }
    },

    async deleteProduct(productId) {
        if (!confirm('هل أنت متأكد من حذف هذا المنتج؟')) {
            return;
        }

        try {
            const response = await fetch(`/php/api/products.php?id=${productId}`, {
                method: 'DELETE'
            });

            const data = await response.json();

            if (data.success) {
                this.showNotification('success', 'Product deleted successfully');
                this.loadProducts(); // Refresh product list
            } else {
                throw new Error(data.message);
            }
        } catch (error) {
            console.error('Error deleting product:', error);
            this.showNotification('error', 'Failed to delete product');
        }
    },

    editProduct(product) {
        // Populate form with product data
        const form = document.querySelector('#productForm');
        form.querySelector('#productId').value = product.id;
        form.querySelector('#productTitle').value = product.titre;
        form.querySelector('#productPrice').value = product.prix;
        form.querySelector('#productStock').value = product.stock;
        form.querySelector('#productType').value = product.type;

        // Show specific fields based on product type
        this.toggleSpecificFields(product.type);

        // Populate specific fields
        switch (product.type) {
            case 'book':
                form.querySelector('#bookAuthor').value = product.auteur || '';
                break;
            case 'bag':
                form.querySelector('#bagMaterial').value = product.materiel || '';
                form.querySelector('#bagCapacity').value = product.capacite || '';
                break;
            case 'laptop':
                form.querySelector('#laptopProcessor').value = product.processeur || '';
                form.querySelector('#laptopRam').value = product.ram || '';
                form.querySelector('#laptopStorage').value = product.stockage || '';
                break;
        }

        // Show current image preview if exists
        const imagePreview = form.querySelector('#imagePreview');
        if (product.image_url) {
            imagePreview.src = product.image_url;
            imagePreview.style.display = 'block';
        } else {
            imagePreview.style.display = 'none';
        }

        // Show modal
        document.querySelector('#productModal').style.display = 'flex';
    },

    showNotification(type, message) {
        const notification = document.createElement('div');
        notification.className = `notification ${type}`;
        notification.textContent = message;

        document.body.appendChild(notification);

        setTimeout(() => {
            notification.remove();
        }, 3000);
    }
};

// Initialize product management when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    productManager.init();
});